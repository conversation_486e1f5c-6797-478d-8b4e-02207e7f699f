/* ===== LOGIN PAGE ENHANCEMENTS ===== */

/* Login Page Background */
.login-page {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
  position: relative;
  overflow: hidden;
}

.login-page::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: 
    radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(120, 119, 198, 0.2) 0%, transparent 50%);
  animation: float 6s ease-in-out infinite;
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-20px); }
}

/* Left Side Branding */
.login-left {
  z-index: 2;
  position: relative;
}

.login-left h1 {
  font-size: 3.5rem;
  font-weight: 800;
  margin-bottom: 0.5rem;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
  animation: slideInLeft 1s ease-out;
}

.login-left h2 {
  font-size: 3rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
  animation: slideInLeft 1s ease-out 0.2s both;
}

.login-left h3 {
  font-size: 2.5rem;
  font-weight: 600;
  margin-bottom: 1rem;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
  animation: slideInLeft 1s ease-out 0.4s both;
}

.login-left p {
  font-size: 1.2rem;
  opacity: 0.9;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
  animation: slideInLeft 1s ease-out 0.6s both;
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Logo Enhancement */
.login-logo {
  filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.3));
  animation: fadeInDown 1s ease-out;
}

@keyframes fadeInDown {
  from {
    opacity: 0;
    transform: translateY(-30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Login Form Container */
.login-form {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 
    0 25px 50px rgba(0, 0, 0, 0.15),
    0 0 0 1px rgba(255, 255, 255, 0.1);
  animation: slideInRight 1s ease-out;
  position: relative;
  overflow: hidden;
}

.login-form::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Login Header */
.login-head h3 {
  color: var(--text-primary);
  font-weight: 800;
  font-size: 1.75rem;
  margin-bottom: 0.5rem;
}

.login-head p {
  color: var(--text-secondary);
  font-size: 0.95rem;
  margin-bottom: 0;
}

/* Form Elements Enhancement */
.login-form .form-label {
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 0.5rem;
  font-size: 0.875rem;
}

.login-form .form-control {
  border: 2px solid var(--border-color);
  border-radius: var(--radius-lg);
  padding: 0.875rem 1rem;
  font-size: 0.95rem;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
}

.login-form .form-control:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 
    0 0 0 4px rgba(108, 39, 255, 0.1),
    0 4px 12px rgba(108, 39, 255, 0.15);
  background: rgba(255, 255, 255, 0.95);
  transform: translateY(-2px);
}

.login-form .form-control::placeholder {
  color: var(--text-muted);
  font-weight: 400;
}

/* Login Button Enhancement */
.login-btn {
  background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
  border: none;
  border-radius: var(--radius-lg);
  padding: 0.875rem 2rem;
  font-weight: 700;
  font-size: 0.95rem;
  color: white;
  width: 100%;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  box-shadow: 
    0 4px 15px rgba(108, 39, 255, 0.3),
    0 2px 4px rgba(0, 0, 0, 0.1);
}

.login-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.login-btn:hover {
  background: linear-gradient(135deg, var(--primary-dark), var(--primary-color));
  transform: translateY(-2px);
  box-shadow: 
    0 8px 25px rgba(108, 39, 255, 0.4),
    0 4px 8px rgba(0, 0, 0, 0.15);
}

.login-btn:hover::before {
  left: 100%;
}

.login-btn:active {
  transform: translateY(0);
}

.login-btn:disabled {
  background: var(--text-muted);
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.login-btn:disabled::before {
  display: none;
}

/* Message Styling */
.response-msg {
  background: rgba(52, 199, 89, 0.1);
  border: 1px solid rgba(52, 199, 89, 0.3);
  color: var(--success-color);
  padding: 0.75rem 1rem;
  border-radius: var(--radius-md);
  font-weight: 600;
  text-align: center;
  animation: slideInUp 0.3s ease-out;
}

.error-msg {
  background: rgba(255, 59, 48, 0.1);
  border: 1px solid rgba(255, 59, 48, 0.3);
  color: var(--danger-color);
  padding: 0.75rem 1rem;
  border-radius: var(--radius-md);
  font-weight: 600;
  text-align: center;
  animation: slideInUp 0.3s ease-out;
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Development Credentials Box */
.login-form .bg-light {
  background: rgba(108, 39, 255, 0.05) !important;
  border: 1px solid rgba(108, 39, 255, 0.2);
  border-radius: var(--radius-md);
  padding: 1rem;
}

.login-form .text-info {
  color: var(--primary-color) !important;
  font-size: 0.8rem;
  line-height: 1.5;
}

/* Loading Spinner Enhancement */
.spinner-border-sm {
  width: 1rem;
  height: 1rem;
  border-width: 2px;
}

/* Responsive Design */
@media (max-width: 992px) {
  .login-left h1 {
    font-size: 2.5rem;
  }
  
  .login-left h2 {
    font-size: 2.2rem;
  }
  
  .login-left h3 {
    font-size: 2rem;
  }
  
  .login-left p {
    font-size: 1rem;
  }
}

@media (max-width: 768px) {
  .login-page .row {
    flex-direction: column-reverse;
  }
  
  .login-left {
    text-align: center;
    padding: 2rem 1rem;
  }
  
  .login-left h1 {
    font-size: 2rem;
  }
  
  .login-left h2 {
    font-size: 1.8rem;
  }
  
  .login-left h3 {
    font-size: 1.6rem;
  }
  
  .login-form {
    margin: 1rem;
    border-radius: var(--radius-xl);
  }
  
  .login-logo {
    max-width: 250px;
  }
}

@media (max-width: 480px) {
  .login-form {
    margin: 0.5rem;
    padding: 1.5rem;
  }
  
  .login-head h3 {
    font-size: 1.5rem;
  }
  
  .login-left h1 {
    font-size: 1.8rem;
  }
  
  .login-left h2 {
    font-size: 1.6rem;
  }
  
  .login-left h3 {
    font-size: 1.4rem;
  }
  
  .login-left p {
    font-size: 0.9rem;
  }
}

/* Accessibility Enhancements */
.login-form .form-control:focus {
  outline: 2px solid var(--primary-color);
  outline-offset: 2px;
}

.login-btn:focus {
  outline: 2px solid var(--primary-color);
  outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .login-form {
    background: white;
    border: 2px solid black;
  }
  
  .login-form .form-control {
    border: 2px solid black;
  }
  
  .login-btn {
    background: black;
    border: 2px solid black;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .login-page::before,
  .login-left h1,
  .login-left h2,
  .login-left h3,
  .login-left p,
  .login-logo,
  .login-form {
    animation: none;
  }
  
  .login-form .form-control:focus {
    transform: none;
  }
  
  .login-btn:hover {
    transform: none;
  }
}
