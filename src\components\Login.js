'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import Image from 'next/image';
import styles from '../app/login/login.module.css';

export default function Login() {
  const [formData, setFormData] = useState({
    username: '',
    password: ''
  });
  const [isLoading, setIsLoading] = useState(false);
  const [message, setMessage] = useState('');
  const [messageType, setMessageType] = useState(''); // 'success' or 'error'
  const router = useRouter();

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsLoading(true);
    setMessage('');

    try {
      // Use development auth in development mode
      const authEndpoint = process.env.NODE_ENV === 'development' ? '/api/auth/dev' : '/api/auth';

      const response = await fetch(authEndpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });

      const data = await response.json();

      if (data.success) {
        setMessage('Login successful! Redirecting...');
        setMessageType('success');

        // Store user data in localStorage for client-side access
        if (typeof window !== 'undefined') {
          localStorage.setItem('user', JSON.stringify({
            token: data.token,
            email: data.user_email,
            displayName: data.user_display_name,
            nicename: data.user_nicename,
            role: data.role
          }));
        }

        // Redirect immediately - the cookie is now set by the API
        const urlParams = new URLSearchParams(window.location.search);
        const callbackUrl = urlParams.get('callbackUrl') || '/dashboard';

        // Use window.location.href for a full page reload to ensure middleware runs
        window.location.href = callbackUrl;
      } else {
        setMessage(data.message || 'Login failed. Please check your credentials.');
        setMessageType('error');
      }
    } catch (error) {
      console.error('Login error:', error);
      setMessage('An error occurred during login. Please try again.');
      setMessageType('error');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="connect360-login-page">
      <div className="container-fluid h-100">
        <div className="row h-100">
          {/* Left Side - Branding */}
          <div className="col-lg-7 d-flex align-items-center justify-content-start text-white connect360-left-section">
            <div className="connect360-branding">
              <div className="connect360-logo mb-5">
                <Image
                  src="/assets/images/login-logo-360-white.png"
                  alt="Occams Connect 360"
                  width={200}
                  height={60}
                  priority
                />
              </div>
              <div className="connect360-content">
                <h1 className="connect360-title">Unlock Seamless</h1>
                <h2 className="connect360-subtitle">Connectivity with</h2>
                <h3 className="connect360-brand">Connect 360</h3>
                <p className="connect360-description">Effortless Access and Streamlined Collaboration</p>
              </div>
            </div>
          </div>

          {/* Right Side - Login Form */}
          <div className="col-lg-5 d-flex align-items-center justify-content-center connect360-right-section">
            <div className="connect360-form-container">
              <div className="connect360-form-card">
                <div className="connect360-form-header">
                  <h3>Sign in</h3>
                </div>

                <form onSubmit={handleSubmit} className="connect360-form">
                  <div className="connect360-form-group">
                    <label htmlFor="username" className="connect360-label">
                      Username
                    </label>
                    <input
                      type="text"
                      className="connect360-input"
                      id="username"
                      name="username"
                      value={formData.username}
                      onChange={handleChange}
                      required
                      disabled={isLoading}
                      placeholder=""
                    />
                  </div>

                  <div className="connect360-form-group">
                    <label htmlFor="password" className="connect360-label">
                      Password
                    </label>
                    <input
                      type="password"
                      className="connect360-input"
                      id="password"
                      name="password"
                      value={formData.password}
                      onChange={handleChange}
                      required
                      disabled={isLoading}
                      placeholder=""
                    />
                  </div>

                  <div className="connect360-form-group">
                    <button
                      type="submit"
                      className="connect360-login-btn"
                      disabled={isLoading}
                    >
                      {isLoading ? (
                        <>
                          <span className="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                          Signing In...
                        </>
                      ) : (
                        'Login'
                      )}
                    </button>
                  </div>

                  {message && (
                    <div className={messageType === 'success' ? 'connect360-success-msg' : 'connect360-error-msg'}>
                      {message}
                    </div>
                  )}
                </form>

                {process.env.NODE_ENV === 'development' && (
                  <div className="connect360-dev-info">
                    <small>
                      <strong>Dev Mode:</strong> admin/admin123, user/user123, demo/demo123
                    </small>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="connect360-footer">
          <p>Copyright © 2025 Occams Portal. All rights reserved.</p>
        </div>
      </div>
    </div>
  );
}
