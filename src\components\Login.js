'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import Image from 'next/image';
import styles from '../app/login/login.module.css';

export default function Login() {
  const [formData, setFormData] = useState({
    username: '',
    password: ''
  });
  const [isLoading, setIsLoading] = useState(false);
  const [message, setMessage] = useState('');
  const [messageType, setMessageType] = useState(''); // 'success' or 'error'
  const router = useRouter();

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsLoading(true);
    setMessage('');

    try {
      const response = await fetch('/api/auth', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });

      const data = await response.json();

      if (data.success) {
        setMessage('Login successful! Redirecting...');
        setMessageType('success');
        
        // Store user data in localStorage or context if needed
        if (typeof window !== 'undefined') {
          localStorage.setItem('user', JSON.stringify({
            token: data.token,
            email: data.user_email,
            displayName: data.user_display_name,
            nicename: data.user_nicename
          }));
        }

        // Redirect to dashboard or callback URL
        const urlParams = new URLSearchParams(window.location.search);
        const callbackUrl = urlParams.get('callbackUrl') || '/dashboard';
        
        setTimeout(() => {
          router.push(callbackUrl);
        }, 1000);
      } else {
        setMessage(data.message || 'Login failed. Please check your credentials.');
        setMessageType('error');
      }
    } catch (error) {
      console.error('Login error:', error);
      setMessage('An error occurred during login. Please try again.');
      setMessageType('error');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className={styles['login-page']}>
      <div className="container-fluid h-100">
        <div className="row h-100">
          {/* Left Side - Branding */}
          <div className="col-lg-6 d-flex align-items-center justify-content-center text-white">
            <div className="text-center">
              <div className="mb-4">
                <Image
                  src="/assets/images/login-logo-360-white.png"
                  alt="Occams Portal"
                  width={360}
                  height={120}
                  className={styles['login-logo']}
                  priority
                />
              </div>
              <div className="login-left">
                <h1>Welcome to</h1>
                <h2>Occams</h2>
                <h3>Portal</h3>
                <p>Your gateway to comprehensive business solutions</p>
              </div>
            </div>
          </div>

          {/* Right Side - Login Form */}
          <div className="col-lg-6 d-flex align-items-center justify-content-center">
            <div className="w-100" style={{ maxWidth: '400px' }}>
              <div className={`bg-white ${styles['login-form']}`}>
                <div className={`text-center ${styles['login-head']}`}>
                  <h3>Sign In</h3>
                  <p className="text-muted">Enter your credentials to access your account</p>
                </div>

                <form onSubmit={handleSubmit}>
                  <div className="mb-3">
                    <label htmlFor="username" className="form-label">
                      Username or Email
                    </label>
                    <input
                      type="text"
                      className="form-control"
                      id="username"
                      name="username"
                      value={formData.username}
                      onChange={handleChange}
                      required
                      disabled={isLoading}
                      placeholder="Enter your username or email"
                    />
                  </div>

                  <div className="mb-3">
                    <label htmlFor="password" className="form-label">
                      Password
                    </label>
                    <input
                      type="password"
                      className="form-control"
                      id="password"
                      name="password"
                      value={formData.password}
                      onChange={handleChange}
                      required
                      disabled={isLoading}
                      placeholder="Enter your password"
                    />
                  </div>

                  <div className="mb-3">
                    <button
                      type="submit"
                      className={styles['login-btn']}
                      disabled={isLoading}
                    >
                      {isLoading ? (
                        <>
                          <span className="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                          Signing In...
                        </>
                      ) : (
                        'Sign In'
                      )}
                    </button>
                  </div>

                  {message && (
                    <div className={messageType === 'success' ? styles['response-msg'] : styles['error-msg']}>
                      {message}
                    </div>
                  )}
                </form>

                <div className="text-center mt-3">
                  <small className="text-muted">
                    Need help? Contact your system administrator
                  </small>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
