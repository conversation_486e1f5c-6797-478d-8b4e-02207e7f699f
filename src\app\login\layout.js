'use client';

import React from 'react';
import '../../assets/css/style.css';

export default function LoginLayout({ children }) {
  // Add login-page-body class to body when component mounts
  React.useEffect(() => {
    document.body.classList.add('login-page-body', 'connect360-body');
    document.body.classList.remove('dashboard_part', 'large_header_bg');

    // Remove the class when component unmounts
    return () => {
      document.body.classList.remove('login-page-body', 'connect360-body');
    };
  }, []);

  return (
    <div className="login-layout">
      {children}
    </div>
  );
}
