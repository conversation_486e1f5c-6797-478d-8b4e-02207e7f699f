/* ===== RESPONSIVE DESIGN ENHANCEMENTS ===== */

/* App Layout Structure */
.app-layout {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.main_content {
  flex: 1;
  display: flex;
  min-height: calc(100vh - 80px);
}

.main_content_iner {
  flex: 1;
  overflow-x: hidden;
}

/* ===== MOBILE FIRST RESPONSIVE DESIGN ===== */

/* Extra Small Devices (phones, 576px and down) */
@media (max-width: 575.98px) {
  .sidebar {
    position: fixed;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100vh;
    z-index: 1050;
    transition: left 0.3s ease;
  }
  
  .sidebar.active_sidebar {
    left: 0;
  }
  
  .main_content {
    flex-direction: column;
  }
  
  .main_content_iner {
    padding: 0.75rem;
  }
  
  .header_iner {
    padding: 0.75rem 1rem;
    flex-wrap: wrap;
    gap: 1rem;
  }
  
  .serach_field-area .search_field input {
    width: 100%;
    min-width: 200px;
  }
  
  .profile_info img {
    width: 32px;
    height: 32px;
  }
  
  .profile_author_name p {
    font-size: 0.75rem;
  }
  
  .profile_author_name h5 {
    font-size: 0.7rem;
  }
  
  .white_card {
    border-radius: var(--radius-lg);
    margin-bottom: 1rem;
  }
  
  .white_card_header {
    padding: 1rem;
  }
  
  .white_card_body {
    padding: 1rem;
  }
  
  .btn {
    padding: 0.625rem 1rem;
    font-size: 0.8rem;
  }
  
  .form-control {
    padding: 0.625rem 0.75rem;
    font-size: 0.875rem;
  }
  
  /* Table responsive */
  .table-responsive {
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-sm);
  }
  
  .table th,
  .table td {
    padding: 0.5rem;
    font-size: 0.8rem;
  }
  
  /* Action buttons mobile */
  .action_btns {
    flex-direction: column;
    gap: 0.25rem;
  }
  
  .action_btn {
    width: 28px;
    height: 28px;
    font-size: 0.75rem;
  }
}

/* Small Devices (landscape phones, 576px and up) */
@media (min-width: 576px) and (max-width: 767.98px) {
  .sidebar {
    width: 250px;
    position: fixed;
    left: -250px;
    transition: left 0.3s ease;
  }
  
  .sidebar.active_sidebar {
    left: 0;
  }
  
  .main_content_iner {
    padding: 1rem;
  }
  
  .serach_field-area .search_field input {
    width: 250px;
  }
  
  .stat-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
  }
  
  .modern-card-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.75rem;
  }
}

/* Medium Devices (tablets, 768px and up) */
@media (min-width: 768px) and (max-width: 991.98px) {
  .sidebar {
    width: 260px;
  }
  
  .main_content_iner {
    padding: 1.25rem;
  }
  
  .serach_field-area .search_field input {
    width: 280px;
  }
  
  .stat-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 1.25rem;
  }
  
  .chart-container {
    height: 350px;
  }
  
  .modern-card-header {
    flex-direction: row;
    flex-wrap: wrap;
    gap: 1rem;
  }
  
  .chart-type-selector {
    flex-wrap: wrap;
  }
}

/* Large Devices (desktops, 992px and up) */
@media (min-width: 992px) and (max-width: 1199.98px) {
  .sidebar {
    width: 270px;
  }
  
  .serach_field-area .search_field input {
    width: 300px;
  }
  
  .stat-grid {
    grid-template-columns: repeat(4, 1fr);
    gap: 1.5rem;
  }
  
  .chart-container {
    height: 380px;
  }
}

/* Extra Large Devices (large desktops, 1200px and up) */
@media (min-width: 1200px) {
  .sidebar {
    width: 280px;
  }
  
  .serach_field-area .search_field input {
    width: 350px;
  }
  
  .stat-grid {
    grid-template-columns: repeat(4, 1fr);
    gap: 1.5rem;
  }
  
  .chart-container {
    height: 400px;
  }
  
  .dashboard-container {
    max-width: 1400px;
  }
}

/* ===== SIDEBAR MOBILE ENHANCEMENTS ===== */
.sidebar_close_icon {
  display: none;
  position: absolute;
  top: 1rem;
  right: 1rem;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  color: white;
  cursor: pointer;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.sidebar_close_icon:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: scale(1.1);
}

@media (max-width: 991.98px) {
  .sidebar_close_icon {
    display: flex;
  }
}

/* Mobile Sidebar Toggle */
.sidebar_icon {
  display: none;
  width: 40px;
  height: 40px;
  border-radius: var(--radius-md);
  background: var(--bg-tertiary);
  color: var(--text-secondary);
  cursor: pointer;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.sidebar_icon:hover {
  background: var(--primary-color);
  color: white;
}

@media (max-width: 991.98px) {
  .sidebar_icon {
    display: flex;
  }
}

/* ===== MOBILE NAVIGATION ENHANCEMENTS ===== */
@media (max-width: 991.98px) {
  .metismenu li a {
    padding: 1rem 1.5rem;
    font-size: 1rem;
  }
  
  .metismenu li a i {
    width: 24px;
    height: 24px;
    font-size: 18px;
    margin-right: 1rem;
  }
  
  .metismenu .mm-collapse li a {
    padding: 0.75rem 1rem 0.75rem 3.5rem;
    font-size: 0.9rem;
  }
}

/* ===== MOBILE FORM ENHANCEMENTS ===== */
@media (max-width: 767.98px) {
  .form-group {
    margin-bottom: 1rem;
  }
  
  .form-control {
    font-size: 16px; /* Prevents zoom on iOS */
  }
  
  .btn {
    width: 100%;
    margin-bottom: 0.5rem;
  }
  
  .btn + .btn {
    margin-left: 0;
  }
  
  .row .col-md-6 {
    margin-bottom: 1rem;
  }
}

/* ===== MOBILE TABLE ENHANCEMENTS ===== */
@media (max-width: 767.98px) {
  .table-responsive {
    border: none;
    box-shadow: none;
  }
  
  .table {
    font-size: 0.8rem;
  }
  
  .table th,
  .table td {
    padding: 0.5rem 0.25rem;
    white-space: nowrap;
  }
  
  .table th:first-child,
  .table td:first-child {
    padding-left: 0.5rem;
  }
  
  .table th:last-child,
  .table td:last-child {
    padding-right: 0.5rem;
  }
}

/* ===== MOBILE CARD STACK ===== */
@media (max-width: 767.98px) {
  .row > [class*="col-"] {
    margin-bottom: 1rem;
  }
  
  .row > [class*="col-"]:last-child {
    margin-bottom: 0;
  }
}

/* ===== TOUCH ENHANCEMENTS ===== */
@media (hover: none) and (pointer: coarse) {
  .btn,
  .action_btn,
  .profile_info,
  .bell_notification_clicker,
  .metismenu li a {
    min-height: 44px; /* iOS touch target minimum */
    min-width: 44px;
  }
  
  .form-control {
    min-height: 44px;
  }
  
  .sidebar_icon,
  .sidebar_close_icon {
    min-height: 44px;
    min-width: 44px;
  }
}

/* ===== LANDSCAPE ORIENTATION ===== */
@media (max-height: 500px) and (orientation: landscape) {
  .sidebar {
    width: 200px;
  }
  
  .metismenu li a {
    padding: 0.5rem 1rem;
  }
  
  .metismenu .nav_title {
    padding: 0.5rem 1rem 0.25rem;
  }
  
  .stat-card {
    padding: 1rem;
  }
  
  .stat-card-value {
    font-size: 1.8rem;
  }
  
  .chart-container {
    height: 250px;
  }
}

/* ===== PRINT STYLES ===== */
@media print {
  .sidebar,
  .header_iner,
  .footer_part,
  .btn,
  .action_btns {
    display: none !important;
  }
  
  .main_content_iner {
    padding: 0 !important;
    margin: 0 !important;
  }
  
  .white_card {
    box-shadow: none !important;
    border: 1px solid #ddd !important;
    break-inside: avoid;
  }
  
  .chart-container {
    height: 300px !important;
  }
}

/* ===== ACCESSIBILITY ENHANCEMENTS ===== */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* High contrast mode */
@media (prefers-contrast: high) {
  .white_card {
    border: 2px solid black;
  }
  
  .btn {
    border: 2px solid black;
  }
  
  .form-control {
    border: 2px solid black;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  :root {
    --bg-primary: #0f172a;
    --bg-secondary: #1e293b;
    --bg-tertiary: #334155;
    --text-primary: #f1f5f9;
    --text-secondary: #cbd5e1;
    --text-muted: #64748b;
    --border-color: #475569;
    --border-light: #334155;
  }
}
