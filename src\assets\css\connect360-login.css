/* ===== CONNECT 360 LOGIN PAGE - EXACT MATCH ===== */

/* Body styling for Connect 360 */
body.connect360-body {
  margin: 0 !important;
  padding: 0 !important;
  font-family: 'Mulish', sans-serif !important;
  overflow-x: hidden;
}

.connect360-login-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #1e88e5 0%, #ff7043 100%);
  position: relative;
  overflow: hidden;
}

.connect360-login-page::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 20% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 80%, rgba(255, 255, 255, 0.08) 0%, transparent 50%),
    radial-gradient(circle at 40% 60%, rgba(255, 255, 255, 0.05) 0%, transparent 50%);
  pointer-events: none;
}

.connect360-login-page .container-fluid {
  position: relative;
  z-index: 2;
  min-height: 100vh;
  padding: 0;
}

.connect360-login-page .row {
  margin: 0;
  min-height: 100vh;
}

/* ===== LEFT SECTION - BRANDING ===== */
.connect360-left-section {
  padding: 60px 80px;
  position: relative;
}

.connect360-branding {
  max-width: 500px;
}

.connect360-logo {
  margin-bottom: 60px !important;
}

.connect360-logo img {
  filter: brightness(0) invert(1);
  max-width: 180px;
  height: auto;
}

.connect360-content {
  color: white;
}

.connect360-title {
  font-size: 3.5rem;
  font-weight: 300;
  line-height: 1.1;
  margin-bottom: 0;
  color: white;
  font-family: 'Mulish', sans-serif;
}

.connect360-subtitle {
  font-size: 3.5rem;
  font-weight: 300;
  line-height: 1.1;
  margin-bottom: 0;
  color: white;
  font-family: 'Mulish', sans-serif;
}

.connect360-brand {
  font-size: 4rem;
  font-weight: 700;
  line-height: 1.1;
  margin-bottom: 20px;
  color: white;
  font-family: 'Mulish', sans-serif;
}

.connect360-description {
  font-size: 1.1rem;
  font-weight: 400;
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: 0;
  line-height: 1.4;
  font-family: 'Mulish', sans-serif;
}

/* ===== RIGHT SECTION - FORM ===== */
.connect360-right-section {
  padding: 40px;
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
}

.connect360-form-container {
  width: 100%;
  max-width: 400px;
  margin: 0 auto;
}

.connect360-form-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 20px;
  padding: 40px;
  box-shadow:
    0 25px 50px rgba(0, 0, 0, 0.15),
    0 0 0 1px rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.connect360-form-header {
  text-align: center;
  margin-bottom: 30px;
}

.connect360-form-header h3 {
  font-size: 1.8rem;
  font-weight: 600;
  color: #1565c0;
  margin: 0;
  font-family: 'Mulish', sans-serif;
}

/* ===== FORM STYLING ===== */
.connect360-form {
  width: 100%;
}

.connect360-form-group {
  margin-bottom: 20px;
}

.connect360-label {
  display: block;
  font-size: 0.9rem;
  font-weight: 500;
  color: #424242;
  margin-bottom: 8px;
  font-family: 'Mulish', sans-serif;
}

.connect360-input {
  width: 100%;
  padding: 12px 16px;
  border: 2px solid #e0e0e0;
  border-radius: 8px;
  font-size: 0.95rem;
  font-family: 'Mulish', sans-serif;
  background: #fafafa;
  transition: all 0.3s ease;
  outline: none;
}

.connect360-input:focus {
  border-color: #1565c0;
  background: white;
  box-shadow: 0 0 0 3px rgba(21, 101, 192, 0.1);
}

.connect360-input:disabled {
  background: #f5f5f5;
  cursor: not-allowed;
}

/* ===== LOGIN BUTTON ===== */
.connect360-login-btn {
  width: 100%;
  padding: 14px 20px;
  background: linear-gradient(135deg, #ff7043, #ff5722);
  border: none;
  border-radius: 8px;
  color: white;
  font-size: 1rem;
  font-weight: 600;
  font-family: 'Mulish', sans-serif;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(255, 112, 67, 0.3);
  margin-top: 10px;
}

.connect360-login-btn:hover:not(:disabled) {
  background: linear-gradient(135deg, #ff5722, #e64a19);
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(255, 112, 67, 0.4);
}

.connect360-login-btn:active {
  transform: translateY(0);
}

.connect360-login-btn:disabled {
  background: #bdbdbd;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

/* ===== MESSAGE STYLING ===== */
.connect360-success-msg {
  background: rgba(76, 175, 80, 0.1);
  border: 1px solid rgba(76, 175, 80, 0.3);
  color: #388e3c;
  padding: 12px 16px;
  border-radius: 8px;
  font-size: 0.9rem;
  font-weight: 500;
  text-align: center;
  margin-top: 15px;
}

.connect360-error-msg {
  background: rgba(244, 67, 54, 0.1);
  border: 1px solid rgba(244, 67, 54, 0.3);
  color: #d32f2f;
  padding: 12px 16px;
  border-radius: 8px;
  font-size: 0.9rem;
  font-weight: 500;
  text-align: center;
  margin-top: 15px;
}

/* ===== DEV INFO ===== */
.connect360-dev-info {
  margin-top: 20px;
  padding: 12px;
  background: rgba(33, 150, 243, 0.1);
  border: 1px solid rgba(33, 150, 243, 0.2);
  border-radius: 8px;
  text-align: center;
}

.connect360-dev-info small {
  color: #1565c0;
  font-size: 0.8rem;
  line-height: 1.4;
}

/* ===== FOOTER ===== */
.connect360-footer {
  position: absolute;
  bottom: 20px;
  left: 0;
  right: 0;
  text-align: center;
  z-index: 3;
}

.connect360-footer p {
  color: rgba(255, 255, 255, 0.8);
  font-size: 0.85rem;
  margin: 0;
  font-family: 'Mulish', sans-serif;
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 991.98px) {
  .connect360-left-section {
    padding: 40px;
    text-align: center;
  }

  .connect360-title,
  .connect360-subtitle {
    font-size: 2.5rem;
  }

  .connect360-brand {
    font-size: 3rem;
  }

  .connect360-description {
    font-size: 1rem;
  }

  .connect360-right-section {
    padding: 20px;
  }

  .connect360-form-card {
    padding: 30px;
  }
}

@media (max-width: 767.98px) {
  .connect360-login-page .row {
    flex-direction: column-reverse;
  }

  .connect360-left-section {
    padding: 30px 20px;
    min-height: auto;
  }

  .connect360-logo {
    margin-bottom: 30px !important;
  }

  .connect360-title,
  .connect360-subtitle {
    font-size: 2rem;
  }

  .connect360-brand {
    font-size: 2.5rem;
  }

  .connect360-description {
    font-size: 0.95rem;
  }

  .connect360-right-section {
    padding: 20px;
    background: transparent;
  }

  .connect360-form-card {
    padding: 25px;
    border-radius: 16px;
  }

  .connect360-footer {
    position: relative;
    bottom: auto;
    margin-top: 20px;
    padding: 20px;
  }
}

@media (max-width: 575.98px) {
  .connect360-left-section {
    padding: 20px 15px;
  }

  .connect360-title,
  .connect360-subtitle {
    font-size: 1.8rem;
  }

  .connect360-brand {
    font-size: 2.2rem;
  }

  .connect360-form-card {
    padding: 20px;
    margin: 0 10px;
  }

  .connect360-form-header h3 {
    font-size: 1.5rem;
  }
}

/* ===== LOADING SPINNER ===== */
.spinner-border-sm {
  width: 1rem;
  height: 1rem;
  border-width: 2px;
}

/* ===== ACCESSIBILITY ===== */
@media (prefers-reduced-motion: reduce) {
  .connect360-login-btn,
  .connect360-input {
    transition: none;
  }
}

@media (prefers-contrast: high) {
  .connect360-form-card {
    background: white;
    border: 2px solid black;
  }

  .connect360-input {
    border: 2px solid black;
  }

  .connect360-login-btn {
    background: black;
    border: 2px solid black;
  }
}
