import AppLayout from '@/components/AppLayout';

export const metadata = {
  title: 'My Profile - Occams Portal',
  description: 'View and edit your profile',
};

export default function MyProfilePage() {
  return (
    <AppLayout>
      <div className="main_content_iner">
        <div className="container-fluid p-0">
          <div className="dashboard-container">
            <div className="dashboard-header">
              <h1 className="dashboard-title">My Profile</h1>
              <p className="dashboard-subtitle">View and edit your profile information</p>
            </div>
            
            {/* My Profile content will go here */}
            <div className="modern-card">
              <div className="modern-card-header">
                <h3 className="modern-card-title">Profile Information</h3>
              </div>
              <div className="modern-card-body">
                <p>This page is under construction.</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </AppLayout>
  );
}
