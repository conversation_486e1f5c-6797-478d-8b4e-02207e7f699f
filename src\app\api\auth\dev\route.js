import { NextResponse } from 'next/server';

// Development authentication route - FOR TESTING ONLY
// This bypasses WordPress and allows local testing
export async function POST(request) {
  try {
    const body = await request.json();
    const { username, password } = body;

    // Development test credentials - REMOVE IN PRODUCTION
    const testUsers = [
      {
        username: 'admin',
        password: 'admin123',
        user_email: '<EMAIL>',
        user_display_name: 'Test Admin',
        user_nicename: 'admin',
        role: 'administrator'
      },
      {
        username: 'user',
        password: 'user123',
        user_email: '<EMAIL>',
        user_display_name: 'Test User',
        user_nicename: 'user',
        role: 'user'
      },
      {
        username: 'demo',
        password: 'demo123',
        user_email: '<EMAIL>',
        user_display_name: 'Demo User',
        user_nicename: 'demo',
        role: 'user'
      }
    ];

    // Find matching user
    const user = testUsers.find(u => 
      u.username === username && u.password === password
    );

    if (user) {
      // Generate a simple token for development
      const token = `dev-token-${user.username}-${Date.now()}`;
      
      return NextResponse.json({
        success: true,
        token: token,
        user_email: user.user_email,
        user_display_name: user.user_display_name,
        user_nicename: user.user_nicename,
        role: user.role
      });
    } else {
      return NextResponse.json(
        { 
          success: false, 
          message: 'Invalid credentials. Try: admin/admin123, user/user123, or demo/demo123' 
        },
        { status: 401 }
      );
    }
  } catch (error) {
    console.error('Dev Auth error:', error);
    return NextResponse.json(
      { success: false, message: 'An error occurred during authentication' },
      { status: 500 }
    );
  }
}
