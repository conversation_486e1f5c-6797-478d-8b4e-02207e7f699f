import AppLayout from '@/components/AppLayout';
import CreateUserForm from '@/components/CreateUserForm'; // Import the new form component

export const metadata = {
  title: 'Create User - Occams Portal',
  description: 'Create a new user in the Occams Portal system.', // Enhanced description
};

export default function CreateUserPage() {
  return (
    <AppLayout>
      {/* The main_content_iner and container-fluid p-0 are part of AppLayout or handled by CreateUserForm if needed */}
      {/* The CreateUserForm component now contains the white_card structure */}
      <CreateUserForm />
    </AppLayout>
  );
}
