import AppLayout from '@/components/AppLayout';

export const metadata = {
  title: 'Analytics - Occams Portal',
  description: 'View analytics',
};

export default function AnalyticsPage() {
  return (
    <AppLayout>
      <div className="main_content_iner">
        <div className="container-fluid p-0">
          <div className="dashboard-container">
            <div className="dashboard-header">
              <h1 className="dashboard-title">Analytics</h1>
              <p className="dashboard-subtitle">View and analyze business performance</p>
            </div>
            
            {/* Analytics content will go here */}
            <div className="modern-card">
              <div className="modern-card-header">
                <h3 className="modern-card-title">Business Analytics</h3>
              </div>
              <div className="modern-card-body">
                <p>This page is under construction.</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </AppLayout>
  );
}
