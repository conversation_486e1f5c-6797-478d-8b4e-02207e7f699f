import { Inter, Mulish } from 'next/font/google';

// Import CSS files
import 'bootstrap/dist/css/bootstrap.min.css';
import '@fortawesome/fontawesome-free/css/all.min.css';

// Import global CSS files
import '../assets/css/style.css';
import '../assets/css/sidebar.css';
import '../assets/css/header-dropdown.css';
import '../assets/css/metisMenu.css';

const inter = Inter({
  subsets: ['latin'],
  variable: '--font-inter',
});

const mulish = Mulish({
  subsets: ['latin'],
  weight: ['200', '300', '400', '500', '600', '700', '800', '900'],
  variable: '--font-mulish',
});

export const metadata = {
  title: "Occams Portal",
  description: "Occams Portal Application",
};

export default function RootLayout({ children }) {
  return (
    <html lang="en">
      <head>
        {/* Font Awesome is imported via npm package above, so CDN link can be removed or commented out
        <link
          rel="stylesheet"
          href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css"
        />
        */}
        {/* Themify Icons CDN */}
        <link
          rel="stylesheet"
          href="https://cdn.jsdelivr.net/npm/themify-icons@0.1.2/css/themify-icons.css"
        />
      </head>
      <body className={`${inter.variable} ${mulish.variable}`}>
        {children}
      </body>
    </html>
  );
}

