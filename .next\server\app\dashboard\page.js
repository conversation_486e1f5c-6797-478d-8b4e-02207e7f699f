/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/dashboard/page";
exports.ids = ["app/dashboard/page"];
exports.modules = {

/***/ "./action-async-storage.external":
/*!****************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external" ***!
  \****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "./request-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fdashboard%2Fpage&page=%2Fdashboard%2Fpage&appPaths=%2Fdashboard%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fpage.js&appDir=C%3A%5Cxampp%5Chtdocs%5Cerc-convert-next%5Cnext%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Cxampp%5Chtdocs%5Cerc-convert-next%5Cnext&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fdashboard%2Fpage&page=%2Fdashboard%2Fpage&appPaths=%2Fdashboard%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fpage.js&appDir=C%3A%5Cxampp%5Chtdocs%5Cerc-convert-next%5Cnext%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Cxampp%5Chtdocs%5Cerc-convert-next%5Cnext&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?9d97\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'dashboard',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/dashboard/page.js */ \"(rsc)/./src/app/dashboard/page.js\")), \"C:\\\\xampp\\\\htdocs\\\\erc-convert-next\\\\next\\\\src\\\\app\\\\dashboard\\\\page.js\"],\n          \n        }]\n      },\n        {\n        \n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.js */ \"(rsc)/./src/app/layout.js\")), \"C:\\\\xampp\\\\htdocs\\\\erc-convert-next\\\\next\\\\src\\\\app\\\\layout.js\"],\n'error': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/error.js */ \"(rsc)/./src/app/error.js\")), \"C:\\\\xampp\\\\htdocs\\\\erc-convert-next\\\\next\\\\src\\\\app\\\\error.js\"],\n'loading': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/loading.js */ \"(rsc)/./src/app/loading.js\")), \"C:\\\\xampp\\\\htdocs\\\\erc-convert-next\\\\next\\\\src\\\\app\\\\loading.js\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/not-found.js */ \"(rsc)/./src/app/not-found.js\")), \"C:\\\\xampp\\\\htdocs\\\\erc-convert-next\\\\next\\\\src\\\\app\\\\not-found.js\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\xampp\\\\htdocs\\\\erc-convert-next\\\\next\\\\src\\\\app\\\\dashboard\\\\page.js\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/dashboard/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/dashboard/page\",\n        pathname: \"/dashboard\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fdashboard%2Fpage&page=%2Fdashboard%2Fpage&appPaths=%2Fdashboard%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fpage.js&appDir=C%3A%5Cxampp%5Chtdocs%5Cerc-convert-next%5Cnext%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Cxampp%5Chtdocs%5Cerc-convert-next%5Cnext&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5Cxampp%5Chtdocs%5Cerc-convert-next%5Cnext%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5Cxampp%5Chtdocs%5Cerc-convert-next%5Cnext%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5Cxampp%5Chtdocs%5Cerc-convert-next%5Cnext%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5Cxampp%5Chtdocs%5Cerc-convert-next%5Cnext%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5Cxampp%5Chtdocs%5Cerc-convert-next%5Cnext%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5Cxampp%5Chtdocs%5Cerc-convert-next%5Cnext%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5Cxampp%5Chtdocs%5Cerc-convert-next%5Cnext%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5Cxampp%5Chtdocs%5Cerc-convert-next%5Cnext%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5Cxampp%5Chtdocs%5Cerc-convert-next%5Cnext%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5Cxampp%5Chtdocs%5Cerc-convert-next%5Cnext%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5Cxampp%5Chtdocs%5Cerc-convert-next%5Cnext%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5Cxampp%5Chtdocs%5Cerc-convert-next%5Cnext%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js */ \"(ssr)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5Cxampp%5Chtdocs%5Cerc-convert-next%5Cnext%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5Cxampp%5Chtdocs%5Cerc-convert-next%5Cnext%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5Cxampp%5Chtdocs%5Cerc-convert-next%5Cnext%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5Cxampp%5Chtdocs%5Cerc-convert-next%5Cnext%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5Cxampp%5Chtdocs%5Cerc-convert-next%5Cnext%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5Cxampp%5Chtdocs%5Cerc-convert-next%5Cnext%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5Cxampp%5Chtdocs%5Cerc-convert-next%5Cnext%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Clink.js&server=true!":
/*!**************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5Cxampp%5Chtdocs%5Cerc-convert-next%5Cnext%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Clink.js&server=true! ***!
  \**************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/link.js */ \"(ssr)/./node_modules/next/dist/client/link.js\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9QyUzQSU1Q3hhbXBwJTVDaHRkb2NzJTVDZXJjLWNvbnZlcnQtbmV4dCU1Q25leHQlNUNub2RlX21vZHVsZXMlNUNuZXh0JTVDZGlzdCU1Q2NsaWVudCU1Q2xpbmsuanMmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZXJjLW5leHQvPzIxNTEiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFx4YW1wcFxcXFxodGRvY3NcXFxcZXJjLWNvbnZlcnQtbmV4dFxcXFxuZXh0XFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0XFxcXGRpc3RcXFxcY2xpZW50XFxcXGxpbmsuanNcIikiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5Cxampp%5Chtdocs%5Cerc-convert-next%5Cnext%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Clink.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5Cxampp%5Chtdocs%5Cerc-convert-next%5Cnext%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22src%5C%5Capp%5C%5Clayout.js%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%2C%22variable%22%3A%22--font-inter%22%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=C%3A%5Cxampp%5Chtdocs%5Cerc-convert-next%5Cnext%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22src%5C%5Capp%5C%5Clayout.js%22%2C%22import%22%3A%22Mulish%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%2C%22weight%22%3A%5B%22200%22%2C%22300%22%2C%22400%22%2C%22500%22%2C%22600%22%2C%22700%22%2C%22800%22%2C%22900%22%5D%2C%22variable%22%3A%22--font-mulish%22%7D%5D%2C%22variableName%22%3A%22mulish%22%7D&modules=C%3A%5Cxampp%5Chtdocs%5Cerc-convert-next%5Cnext%5Cnode_modules%5Cbootstrap%5Cdist%5Ccss%5Cbootstrap.min.css&modules=C%3A%5Cxampp%5Chtdocs%5Cerc-convert-next%5Cnext%5Cnode_modules%5C%40fortawesome%5Cfontawesome-free%5Ccss%5Call.min.css&modules=C%3A%5Cxampp%5Chtdocs%5Cerc-convert-next%5Cnext%5Csrc%5Cassets%5Ccss%5Cstyle.css&modules=C%3A%5Cxampp%5Chtdocs%5Cerc-convert-next%5Cnext%5Csrc%5Cassets%5Ccss%5Csidebar.css&modules=C%3A%5Cxampp%5Chtdocs%5Cerc-convert-next%5Cnext%5Csrc%5Cassets%5Ccss%5Cheader-dropdown.css&modules=C%3A%5Cxampp%5Chtdocs%5Cerc-convert-next%5Cnext%5Csrc%5Cassets%5Ccss%5CmetisMenu.css&modules=C%3A%5Cxampp%5Chtdocs%5Cerc-convert-next%5Cnext%5Csrc%5Cassets%5Ccss%5Cdesign-enhancements.css&modules=C%3A%5Cxampp%5Chtdocs%5Cerc-convert-next%5Cnext%5Csrc%5Cassets%5Ccss%5Cdashboard.css&modules=C%3A%5Cxampp%5Chtdocs%5Cerc-convert-next%5Cnext%5Csrc%5Cassets%5Ccss%5Clogin-enhancements.css&modules=C%3A%5Cxampp%5Chtdocs%5Cerc-convert-next%5Cnext%5Csrc%5Cassets%5Ccss%5Cresponsive-enhancements.css&modules=C%3A%5Cxampp%5Chtdocs%5Cerc-convert-next%5Cnext%5Csrc%5Cassets%5Ccss%5Cconnect360-login.css&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5Cxampp%5Chtdocs%5Cerc-convert-next%5Cnext%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22src%5C%5Capp%5C%5Clayout.js%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%2C%22variable%22%3A%22--font-inter%22%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=C%3A%5Cxampp%5Chtdocs%5Cerc-convert-next%5Cnext%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22src%5C%5Capp%5C%5Clayout.js%22%2C%22import%22%3A%22Mulish%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%2C%22weight%22%3A%5B%22200%22%2C%22300%22%2C%22400%22%2C%22500%22%2C%22600%22%2C%22700%22%2C%22800%22%2C%22900%22%5D%2C%22variable%22%3A%22--font-mulish%22%7D%5D%2C%22variableName%22%3A%22mulish%22%7D&modules=C%3A%5Cxampp%5Chtdocs%5Cerc-convert-next%5Cnext%5Cnode_modules%5Cbootstrap%5Cdist%5Ccss%5Cbootstrap.min.css&modules=C%3A%5Cxampp%5Chtdocs%5Cerc-convert-next%5Cnext%5Cnode_modules%5C%40fortawesome%5Cfontawesome-free%5Ccss%5Call.min.css&modules=C%3A%5Cxampp%5Chtdocs%5Cerc-convert-next%5Cnext%5Csrc%5Cassets%5Ccss%5Cstyle.css&modules=C%3A%5Cxampp%5Chtdocs%5Cerc-convert-next%5Cnext%5Csrc%5Cassets%5Ccss%5Csidebar.css&modules=C%3A%5Cxampp%5Chtdocs%5Cerc-convert-next%5Cnext%5Csrc%5Cassets%5Ccss%5Cheader-dropdown.css&modules=C%3A%5Cxampp%5Chtdocs%5Cerc-convert-next%5Cnext%5Csrc%5Cassets%5Ccss%5CmetisMenu.css&modules=C%3A%5Cxampp%5Chtdocs%5Cerc-convert-next%5Cnext%5Csrc%5Cassets%5Ccss%5Cdesign-enhancements.css&modules=C%3A%5Cxampp%5Chtdocs%5Cerc-convert-next%5Cnext%5Csrc%5Cassets%5Ccss%5Cdashboard.css&modules=C%3A%5Cxampp%5Chtdocs%5Cerc-convert-next%5Cnext%5Csrc%5Cassets%5Ccss%5Clogin-enhancements.css&modules=C%3A%5Cxampp%5Chtdocs%5Cerc-convert-next%5Cnext%5Csrc%5Cassets%5Ccss%5Cresponsive-enhancements.css&modules=C%3A%5Cxampp%5Chtdocs%5Cerc-convert-next%5Cnext%5Csrc%5Cassets%5Ccss%5Cconnect360-login.css&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5Cxampp%5Chtdocs%5Cerc-convert-next%5Cnext%5Csrc%5Capp%5Cerror.js&server=true!":
/*!*************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5Cxampp%5Chtdocs%5Cerc-convert-next%5Cnext%5Csrc%5Capp%5Cerror.js&server=true! ***!
  \*************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/error.js */ \"(ssr)/./src/app/error.js\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9QyUzQSU1Q3hhbXBwJTVDaHRkb2NzJTVDZXJjLWNvbnZlcnQtbmV4dCU1Q25leHQlNUNzcmMlNUNhcHAlNUNlcnJvci5qcyZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9lcmMtbmV4dC8/MWQ1ZSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXHhhbXBwXFxcXGh0ZG9jc1xcXFxlcmMtY29udmVydC1uZXh0XFxcXG5leHRcXFxcc3JjXFxcXGFwcFxcXFxlcnJvci5qc1wiKSJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5Cxampp%5Chtdocs%5Cerc-convert-next%5Cnext%5Csrc%5Capp%5Cerror.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5Cxampp%5Chtdocs%5Cerc-convert-next%5Cnext%5Csrc%5Ccomponents%5CAppLayout.js&modules=C%3A%5Cxampp%5Chtdocs%5Cerc-convert-next%5Cnext%5Csrc%5Ccomponents%5CDashboard.js&server=true!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5Cxampp%5Chtdocs%5Cerc-convert-next%5Cnext%5Csrc%5Ccomponents%5CAppLayout.js&modules=C%3A%5Cxampp%5Chtdocs%5Cerc-convert-next%5Cnext%5Csrc%5Ccomponents%5CDashboard.js&server=true! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/AppLayout.js */ \"(ssr)/./src/components/AppLayout.js\"));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/Dashboard.js */ \"(ssr)/./src/components/Dashboard.js\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9QyUzQSU1Q3hhbXBwJTVDaHRkb2NzJTVDZXJjLWNvbnZlcnQtbmV4dCU1Q25leHQlNUNzcmMlNUNjb21wb25lbnRzJTVDQXBwTGF5b3V0LmpzJm1vZHVsZXM9QyUzQSU1Q3hhbXBwJTVDaHRkb2NzJTVDZXJjLWNvbnZlcnQtbmV4dCU1Q25leHQlNUNzcmMlNUNjb21wb25lbnRzJTVDRGFzaGJvYXJkLmpzJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxzS0FBNkc7QUFDN0ciLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9lcmMtbmV4dC8/NDdmZCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXHhhbXBwXFxcXGh0ZG9jc1xcXFxlcmMtY29udmVydC1uZXh0XFxcXG5leHRcXFxcc3JjXFxcXGNvbXBvbmVudHNcXFxcQXBwTGF5b3V0LmpzXCIpO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFx4YW1wcFxcXFxodGRvY3NcXFxcZXJjLWNvbnZlcnQtbmV4dFxcXFxuZXh0XFxcXHNyY1xcXFxjb21wb25lbnRzXFxcXERhc2hib2FyZC5qc1wiKSJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5Cxampp%5Chtdocs%5Cerc-convert-next%5Cnext%5Csrc%5Ccomponents%5CAppLayout.js&modules=C%3A%5Cxampp%5Chtdocs%5Cerc-convert-next%5Cnext%5Csrc%5Ccomponents%5CDashboard.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./src/app/error.js":
/*!**************************!*\
  !*** ./src/app/error.js ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Error)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction Error({ error, reset }) {\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Log the error to an error reporting service\n        console.error(error);\n    }, [\n        error\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"d-flex flex-column justify-content-center align-items-center vh-100\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                className: \"mb-4\",\n                children: \"Something went wrong!\"\n            }, void 0, false, {\n                fileName: \"C:\\\\xampp\\\\htdocs\\\\erc-convert-next\\\\next\\\\src\\\\app\\\\error.js\",\n                lineNumber: 13,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                className: \"btn btn-primary\",\n                onClick: // Attempt to recover by trying to re-render the segment\n                ()=>reset(),\n                children: \"Try again\"\n            }, void 0, false, {\n                fileName: \"C:\\\\xampp\\\\htdocs\\\\erc-convert-next\\\\next\\\\src\\\\app\\\\error.js\",\n                lineNumber: 14,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\xampp\\\\htdocs\\\\erc-convert-next\\\\next\\\\src\\\\app\\\\error.js\",\n        lineNumber: 12,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvYXBwL2Vycm9yLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7OztBQUVrQztBQUVuQixTQUFTQyxNQUFNLEVBQUVDLEtBQUssRUFBRUMsS0FBSyxFQUFFO0lBQzVDSCxnREFBU0EsQ0FBQztRQUNSLDhDQUE4QztRQUM5Q0ksUUFBUUYsS0FBSyxDQUFDQTtJQUNoQixHQUFHO1FBQUNBO0tBQU07SUFFVixxQkFDRSw4REFBQ0c7UUFBSUMsV0FBVTs7MEJBQ2IsOERBQUNDO2dCQUFHRCxXQUFVOzBCQUFPOzs7Ozs7MEJBQ3JCLDhEQUFDRTtnQkFDQ0YsV0FBVTtnQkFDVkcsU0FDRSx3REFBd0Q7Z0JBQ3hELElBQU1OOzBCQUVUOzs7Ozs7Ozs7Ozs7QUFLUCIsInNvdXJjZXMiOlsid2VicGFjazovL2VyYy1uZXh0Ly4vc3JjL2FwcC9lcnJvci5qcz81MGE1Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50JztcblxuaW1wb3J0IHsgdXNlRWZmZWN0IH0gZnJvbSAncmVhY3QnO1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBFcnJvcih7IGVycm9yLCByZXNldCB9KSB7XG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgLy8gTG9nIHRoZSBlcnJvciB0byBhbiBlcnJvciByZXBvcnRpbmcgc2VydmljZVxuICAgIGNvbnNvbGUuZXJyb3IoZXJyb3IpO1xuICB9LCBbZXJyb3JdKTtcblxuICByZXR1cm4gKFxuICAgIDxkaXYgY2xhc3NOYW1lPVwiZC1mbGV4IGZsZXgtY29sdW1uIGp1c3RpZnktY29udGVudC1jZW50ZXIgYWxpZ24taXRlbXMtY2VudGVyIHZoLTEwMFwiPlxuICAgICAgPGgyIGNsYXNzTmFtZT1cIm1iLTRcIj5Tb21ldGhpbmcgd2VudCB3cm9uZyE8L2gyPlxuICAgICAgPGJ1dHRvblxuICAgICAgICBjbGFzc05hbWU9XCJidG4gYnRuLXByaW1hcnlcIlxuICAgICAgICBvbkNsaWNrPXtcbiAgICAgICAgICAvLyBBdHRlbXB0IHRvIHJlY292ZXIgYnkgdHJ5aW5nIHRvIHJlLXJlbmRlciB0aGUgc2VnbWVudFxuICAgICAgICAgICgpID0+IHJlc2V0KClcbiAgICAgICAgfVxuICAgICAgPlxuICAgICAgICBUcnkgYWdhaW5cbiAgICAgIDwvYnV0dG9uPlxuICAgIDwvZGl2PlxuICApO1xufVxuIl0sIm5hbWVzIjpbInVzZUVmZmVjdCIsIkVycm9yIiwiZXJyb3IiLCJyZXNldCIsImNvbnNvbGUiLCJkaXYiLCJjbGFzc05hbWUiLCJoMiIsImJ1dHRvbiIsIm9uQ2xpY2siXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/app/error.js\n");

/***/ }),

/***/ "(ssr)/./src/components/AppLayout.js":
/*!*************************************!*\
  !*** ./src/components/AppLayout.js ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AppLayout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _Header__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./Header */ \"(ssr)/./src/components/Header.js\");\n/* harmony import */ var _Sidebar__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./Sidebar */ \"(ssr)/./src/components/Sidebar.js\");\n/* harmony import */ var _Footer__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./Footer */ \"(ssr)/./src/components/Footer.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nfunction AppLayout({ children }) {\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Add necessary classes to body for the admin layout\n        document.body.classList.add(\"dashboard_part\", \"large_header_bg\");\n        // Remove login-page-body class if it exists\n        document.body.classList.remove(\"login-page-body\");\n        // Cleanup function to remove classes when component unmounts\n        return ()=>{\n            document.body.classList.remove(\"dashboard_part\", \"large_header_bg\");\n        };\n    }, []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"app-layout\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"header_iner d-flex justify-content-between align-items-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Header__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\xampp\\\\htdocs\\\\erc-convert-next\\\\next\\\\src\\\\components\\\\AppLayout.js\",\n                    lineNumber: 26,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\xampp\\\\htdocs\\\\erc-convert-next\\\\next\\\\src\\\\components\\\\AppLayout.js\",\n                lineNumber: 25,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"main_content dashboard_part large_header_bg d-flex\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Sidebar__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\xampp\\\\htdocs\\\\erc-convert-next\\\\next\\\\src\\\\components\\\\AppLayout.js\",\n                        lineNumber: 32,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"main_content_iner flex-grow-1\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"container-fluid p-0\",\n                            children: children\n                        }, void 0, false, {\n                            fileName: \"C:\\\\xampp\\\\htdocs\\\\erc-convert-next\\\\next\\\\src\\\\components\\\\AppLayout.js\",\n                            lineNumber: 36,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\xampp\\\\htdocs\\\\erc-convert-next\\\\next\\\\src\\\\components\\\\AppLayout.js\",\n                        lineNumber: 35,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\xampp\\\\htdocs\\\\erc-convert-next\\\\next\\\\src\\\\components\\\\AppLayout.js\",\n                lineNumber: 30,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Footer__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\xampp\\\\htdocs\\\\erc-convert-next\\\\next\\\\src\\\\components\\\\AppLayout.js\",\n                lineNumber: 43,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\xampp\\\\htdocs\\\\erc-convert-next\\\\next\\\\src\\\\components\\\\AppLayout.js\",\n        lineNumber: 23,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/AppLayout.js\n");

/***/ }),

/***/ "(ssr)/./src/components/Dashboard.js":
/*!*************************************!*\
  !*** ./src/components/Dashboard.js ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var chart_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! chart.js */ \"(ssr)/./node_modules/chart.js/dist/chart.js\");\n/* harmony import */ var react_chartjs_2__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-chartjs-2 */ \"(ssr)/./node_modules/react-chartjs-2/dist/index.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n// Register ChartJS components\nchart_js__WEBPACK_IMPORTED_MODULE_2__.Chart.register(chart_js__WEBPACK_IMPORTED_MODULE_2__.CategoryScale, chart_js__WEBPACK_IMPORTED_MODULE_2__.LinearScale, chart_js__WEBPACK_IMPORTED_MODULE_2__.PointElement, chart_js__WEBPACK_IMPORTED_MODULE_2__.LineElement, chart_js__WEBPACK_IMPORTED_MODULE_2__.BarElement, chart_js__WEBPACK_IMPORTED_MODULE_2__.Title, chart_js__WEBPACK_IMPORTED_MODULE_2__.Tooltip, chart_js__WEBPACK_IMPORTED_MODULE_2__.Legend, chart_js__WEBPACK_IMPORTED_MODULE_2__.Filler);\nconst Dashboard = ()=>{\n    // Chart data\n    const [chartType, setChartType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"leads\");\n    const leadData = {\n        labels: [\n            \"Jan\",\n            \"Feb\",\n            \"Mar\",\n            \"Apr\",\n            \"May\",\n            \"Jun\"\n        ],\n        datasets: [\n            {\n                label: \"Lead Conversion\",\n                data: [\n                    65,\n                    59,\n                    80,\n                    81,\n                    56,\n                    85\n                ],\n                fill: true,\n                backgroundColor: \"rgba(106, 17, 203, 0.1)\",\n                borderColor: \"rgba(106, 17, 203, 1)\",\n                tension: 0.4\n            },\n            {\n                label: \"Target\",\n                data: [\n                    70,\n                    70,\n                    75,\n                    75,\n                    80,\n                    80\n                ],\n                fill: false,\n                borderColor: \"rgba(255, 99, 132, 1)\",\n                borderDash: [\n                    5,\n                    5\n                ],\n                tension: 0.4\n            }\n        ]\n    };\n    const contactData = {\n        labels: [\n            \"Jan\",\n            \"Feb\",\n            \"Mar\",\n            \"Apr\",\n            \"May\",\n            \"Jun\"\n        ],\n        datasets: [\n            {\n                label: \"New Contacts\",\n                data: [\n                    28,\n                    35,\n                    40,\n                    42,\n                    50,\n                    65\n                ],\n                backgroundColor: \"rgba(26, 115, 232, 0.8)\",\n                borderRadius: 6\n            }\n        ]\n    };\n    const chartOptions = {\n        responsive: true,\n        maintainAspectRatio: false,\n        plugins: {\n            legend: {\n                position: \"top\",\n                labels: {\n                    boxWidth: 10,\n                    usePointStyle: true,\n                    pointStyle: \"circle\"\n                }\n            },\n            tooltip: {\n                backgroundColor: \"rgba(255, 255, 255, 0.9)\",\n                titleColor: \"#2c3e50\",\n                bodyColor: \"#2c3e50\",\n                borderColor: \"#e9ecef\",\n                borderWidth: 1,\n                padding: 12,\n                boxPadding: 6,\n                usePointStyle: true,\n                callbacks: {\n                    labelPointStyle: ()=>({\n                            pointStyle: \"circle\",\n                            rotation: 0\n                        })\n                }\n            }\n        },\n        scales: {\n            y: {\n                beginAtZero: true,\n                grid: {\n                    color: \"rgba(0, 0, 0, 0.05)\",\n                    drawBorder: false\n                },\n                ticks: {\n                    color: \"#7f8c8d\"\n                }\n            },\n            x: {\n                grid: {\n                    display: false\n                },\n                ticks: {\n                    color: \"#7f8c8d\"\n                }\n            }\n        }\n    };\n    // Dashboard statistics\n    const [stats, setStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        totalContacts: 125,\n        activeLeads: 42,\n        completedTasks: 78,\n        pendingTasks: 15,\n        recentActivity: [\n            {\n                id: 1,\n                title: \"New contact added\",\n                subtitle: \"John Doe added a new contact\",\n                time: \"2 hours ago\",\n                icon: \"user-plus\",\n                color: \"bg-purple\"\n            },\n            {\n                id: 2,\n                title: \"Task completed\",\n                subtitle: \"Jane Smith completed a task\",\n                time: \"3 hours ago\",\n                icon: \"check-circle\",\n                color: \"bg-green\"\n            },\n            {\n                id: 3,\n                title: \"Lead converted\",\n                subtitle: \"Admin converted a lead to customer\",\n                time: \"5 hours ago\",\n                icon: \"exchange-alt\",\n                color: \"bg-blue\"\n            },\n            {\n                id: 4,\n                title: \"New task assigned\",\n                subtitle: \"Bob Johnson assigned a new task\",\n                time: \"1 day ago\",\n                icon: \"tasks\",\n                color: \"bg-orange\"\n            }\n        ],\n        supportTickets: [\n            {\n                id: 1,\n                title: \"Need help with contact import\",\n                status: \"Open\",\n                priority: \"High\"\n            },\n            {\n                id: 2,\n                title: \"Email integration not working\",\n                status: \"In Progress\",\n                priority: \"Medium\"\n            }\n        ]\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        document.title = \"Dashboard - Occams Portal\";\n    }, []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"main_content_iner\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container-fluid p-0\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"dashboard-container\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"dashboard-header\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"dashboard-title\",\n                                children: \"Dashboard\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\xampp\\\\htdocs\\\\erc-convert-next\\\\next\\\\src\\\\components\\\\Dashboard.js\",\n                                lineNumber: 147,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"dashboard-subtitle\",\n                                children: \"Welcome back! Here's an overview of your business performance\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\xampp\\\\htdocs\\\\erc-convert-next\\\\next\\\\src\\\\components\\\\Dashboard.js\",\n                                lineNumber: 148,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\xampp\\\\htdocs\\\\erc-convert-next\\\\next\\\\src\\\\components\\\\Dashboard.js\",\n                        lineNumber: 146,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"stat-grid\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"stat-card\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"stat-card-icon bg-purple\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                            className: \"fas fa-address-book\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\xampp\\\\htdocs\\\\erc-convert-next\\\\next\\\\src\\\\components\\\\Dashboard.js\",\n                                            lineNumber: 154,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\erc-convert-next\\\\next\\\\src\\\\components\\\\Dashboard.js\",\n                                        lineNumber: 153,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"stat-card-value\",\n                                        children: stats.totalContacts\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\erc-convert-next\\\\next\\\\src\\\\components\\\\Dashboard.js\",\n                                        lineNumber: 156,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"stat-card-label\",\n                                        children: \"Total Contacts\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\erc-convert-next\\\\next\\\\src\\\\components\\\\Dashboard.js\",\n                                        lineNumber: 157,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\xampp\\\\htdocs\\\\erc-convert-next\\\\next\\\\src\\\\components\\\\Dashboard.js\",\n                                lineNumber: 152,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"stat-card\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"stat-card-icon bg-blue\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                            className: \"fas fa-user-check\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\xampp\\\\htdocs\\\\erc-convert-next\\\\next\\\\src\\\\components\\\\Dashboard.js\",\n                                            lineNumber: 162,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\erc-convert-next\\\\next\\\\src\\\\components\\\\Dashboard.js\",\n                                        lineNumber: 161,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"stat-card-value\",\n                                        children: stats.activeLeads\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\erc-convert-next\\\\next\\\\src\\\\components\\\\Dashboard.js\",\n                                        lineNumber: 164,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"stat-card-label\",\n                                        children: \"Active Leads\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\erc-convert-next\\\\next\\\\src\\\\components\\\\Dashboard.js\",\n                                        lineNumber: 165,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\xampp\\\\htdocs\\\\erc-convert-next\\\\next\\\\src\\\\components\\\\Dashboard.js\",\n                                lineNumber: 160,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"stat-card\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"stat-card-icon bg-green\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                            className: \"fas fa-check-double\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\xampp\\\\htdocs\\\\erc-convert-next\\\\next\\\\src\\\\components\\\\Dashboard.js\",\n                                            lineNumber: 170,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\erc-convert-next\\\\next\\\\src\\\\components\\\\Dashboard.js\",\n                                        lineNumber: 169,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"stat-card-value\",\n                                        children: stats.completedTasks\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\erc-convert-next\\\\next\\\\src\\\\components\\\\Dashboard.js\",\n                                        lineNumber: 172,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"stat-card-label\",\n                                        children: \"Completed Tasks\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\erc-convert-next\\\\next\\\\src\\\\components\\\\Dashboard.js\",\n                                        lineNumber: 173,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\xampp\\\\htdocs\\\\erc-convert-next\\\\next\\\\src\\\\components\\\\Dashboard.js\",\n                                lineNumber: 168,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"stat-card\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"stat-card-icon bg-orange\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                            className: \"fas fa-clock\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\xampp\\\\htdocs\\\\erc-convert-next\\\\next\\\\src\\\\components\\\\Dashboard.js\",\n                                            lineNumber: 178,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\erc-convert-next\\\\next\\\\src\\\\components\\\\Dashboard.js\",\n                                        lineNumber: 177,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"stat-card-value\",\n                                        children: stats.pendingTasks\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\erc-convert-next\\\\next\\\\src\\\\components\\\\Dashboard.js\",\n                                        lineNumber: 180,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"stat-card-label\",\n                                        children: \"Pending Tasks\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\erc-convert-next\\\\next\\\\src\\\\components\\\\Dashboard.js\",\n                                        lineNumber: 181,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\xampp\\\\htdocs\\\\erc-convert-next\\\\next\\\\src\\\\components\\\\Dashboard.js\",\n                                lineNumber: 176,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\xampp\\\\htdocs\\\\erc-convert-next\\\\next\\\\src\\\\components\\\\Dashboard.js\",\n                        lineNumber: 151,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"row\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"col-lg-8 chart-section\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"modern-card\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"modern-card-header\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"modern-card-title\",\n                                                    children: \"Business Performance\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\xampp\\\\htdocs\\\\erc-convert-next\\\\next\\\\src\\\\components\\\\Dashboard.js\",\n                                                    lineNumber: 189,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"chart-type-selector\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            className: `btn btn-sm ${chartType === \"leads\" ? \"btn-primary\" : \"btn-light\"}`,\n                                                            onClick: ()=>setChartType(\"leads\"),\n                                                            children: \"Lead Conversion\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\xampp\\\\htdocs\\\\erc-convert-next\\\\next\\\\src\\\\components\\\\Dashboard.js\",\n                                                            lineNumber: 191,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            className: `btn btn-sm ms-2 ${chartType === \"contacts\" ? \"btn-primary\" : \"btn-light\"}`,\n                                                            onClick: ()=>setChartType(\"contacts\"),\n                                                            children: \"Contact Growth\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\xampp\\\\htdocs\\\\erc-convert-next\\\\next\\\\src\\\\components\\\\Dashboard.js\",\n                                                            lineNumber: 197,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\xampp\\\\htdocs\\\\erc-convert-next\\\\next\\\\src\\\\components\\\\Dashboard.js\",\n                                                    lineNumber: 190,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\xampp\\\\htdocs\\\\erc-convert-next\\\\next\\\\src\\\\components\\\\Dashboard.js\",\n                                            lineNumber: 188,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"modern-card-body\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"chart-container\",\n                                                children: chartType === \"leads\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_chartjs_2__WEBPACK_IMPORTED_MODULE_3__.Line, {\n                                                    data: leadData,\n                                                    options: chartOptions\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\xampp\\\\htdocs\\\\erc-convert-next\\\\next\\\\src\\\\components\\\\Dashboard.js\",\n                                                    lineNumber: 208,\n                                                    columnNumber: 23\n                                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_chartjs_2__WEBPACK_IMPORTED_MODULE_3__.Bar, {\n                                                    data: contactData,\n                                                    options: chartOptions\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\xampp\\\\htdocs\\\\erc-convert-next\\\\next\\\\src\\\\components\\\\Dashboard.js\",\n                                                    lineNumber: 210,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\xampp\\\\htdocs\\\\erc-convert-next\\\\next\\\\src\\\\components\\\\Dashboard.js\",\n                                                lineNumber: 206,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\xampp\\\\htdocs\\\\erc-convert-next\\\\next\\\\src\\\\components\\\\Dashboard.js\",\n                                            lineNumber: 205,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\xampp\\\\htdocs\\\\erc-convert-next\\\\next\\\\src\\\\components\\\\Dashboard.js\",\n                                    lineNumber: 187,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\xampp\\\\htdocs\\\\erc-convert-next\\\\next\\\\src\\\\components\\\\Dashboard.js\",\n                                lineNumber: 186,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"col-lg-4 support-section\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"modern-card\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"modern-card-header\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"modern-card-title\",\n                                                children: \"Support Center\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\xampp\\\\htdocs\\\\erc-convert-next\\\\next\\\\src\\\\components\\\\Dashboard.js\",\n                                                lineNumber: 220,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\xampp\\\\htdocs\\\\erc-convert-next\\\\next\\\\src\\\\components\\\\Dashboard.js\",\n                                            lineNumber: 219,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"modern-card-body\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"support-header\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"support-header-icon\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                                className: \"fas fa-headset\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\xampp\\\\htdocs\\\\erc-convert-next\\\\next\\\\src\\\\components\\\\Dashboard.js\",\n                                                                lineNumber: 225,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\xampp\\\\htdocs\\\\erc-convert-next\\\\next\\\\src\\\\components\\\\Dashboard.js\",\n                                                            lineNumber: 224,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"support-header-content\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    children: \"Need Help?\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\xampp\\\\htdocs\\\\erc-convert-next\\\\next\\\\src\\\\components\\\\Dashboard.js\",\n                                                                    lineNumber: 228,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    children: \"Our support team is available 24/7\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\xampp\\\\htdocs\\\\erc-convert-next\\\\next\\\\src\\\\components\\\\Dashboard.js\",\n                                                                    lineNumber: 229,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\xampp\\\\htdocs\\\\erc-convert-next\\\\next\\\\src\\\\components\\\\Dashboard.js\",\n                                                            lineNumber: 227,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\xampp\\\\htdocs\\\\erc-convert-next\\\\next\\\\src\\\\components\\\\Dashboard.js\",\n                                                    lineNumber: 223,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"support-action\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: \"#\",\n                                                        className: \"support-btn\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                                className: \"fas fa-comment-dots\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\xampp\\\\htdocs\\\\erc-convert-next\\\\next\\\\src\\\\components\\\\Dashboard.js\",\n                                                                lineNumber: 235,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            \"Contact Support\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\erc-convert-next\\\\next\\\\src\\\\components\\\\Dashboard.js\",\n                                                        lineNumber: 234,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\xampp\\\\htdocs\\\\erc-convert-next\\\\next\\\\src\\\\components\\\\Dashboard.js\",\n                                                    lineNumber: 233,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"ticket-list\",\n                                                    children: stats.supportTickets.map((ticket)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"ticket-item\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: `ticket-priority ${ticket.priority === \"High\" ? \"priority-high\" : \"priority-medium\"}`\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\xampp\\\\htdocs\\\\erc-convert-next\\\\next\\\\src\\\\components\\\\Dashboard.js\",\n                                                                    lineNumber: 243,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"ticket-content\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"ticket-title\",\n                                                                            children: ticket.title\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\xampp\\\\htdocs\\\\erc-convert-next\\\\next\\\\src\\\\components\\\\Dashboard.js\",\n                                                                            lineNumber: 245,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"ticket-status\",\n                                                                            children: [\n                                                                                \"Status:\",\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: `ticket-badge ${ticket.status === \"Open\" ? \"badge-open\" : \"badge-progress\"}`,\n                                                                                    children: ticket.status\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\xampp\\\\htdocs\\\\erc-convert-next\\\\next\\\\src\\\\components\\\\Dashboard.js\",\n                                                                                    lineNumber: 248,\n                                                                                    columnNumber: 29\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\xampp\\\\htdocs\\\\erc-convert-next\\\\next\\\\src\\\\components\\\\Dashboard.js\",\n                                                                            lineNumber: 246,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\xampp\\\\htdocs\\\\erc-convert-next\\\\next\\\\src\\\\components\\\\Dashboard.js\",\n                                                                    lineNumber: 244,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, ticket.id, true, {\n                                                            fileName: \"C:\\\\xampp\\\\htdocs\\\\erc-convert-next\\\\next\\\\src\\\\components\\\\Dashboard.js\",\n                                                            lineNumber: 242,\n                                                            columnNumber: 23\n                                                        }, undefined))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\xampp\\\\htdocs\\\\erc-convert-next\\\\next\\\\src\\\\components\\\\Dashboard.js\",\n                                                    lineNumber: 240,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\xampp\\\\htdocs\\\\erc-convert-next\\\\next\\\\src\\\\components\\\\Dashboard.js\",\n                                            lineNumber: 222,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\xampp\\\\htdocs\\\\erc-convert-next\\\\next\\\\src\\\\components\\\\Dashboard.js\",\n                                    lineNumber: 218,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\xampp\\\\htdocs\\\\erc-convert-next\\\\next\\\\src\\\\components\\\\Dashboard.js\",\n                                lineNumber: 217,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\xampp\\\\htdocs\\\\erc-convert-next\\\\next\\\\src\\\\components\\\\Dashboard.js\",\n                        lineNumber: 185,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"row\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"col-12 activity-feed\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"modern-card\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"modern-card-header\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"modern-card-title\",\n                                                children: \"Recent Activity\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\xampp\\\\htdocs\\\\erc-convert-next\\\\next\\\\src\\\\components\\\\Dashboard.js\",\n                                                lineNumber: 265,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"btn btn-sm btn-light\",\n                                                children: \"View All\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\xampp\\\\htdocs\\\\erc-convert-next\\\\next\\\\src\\\\components\\\\Dashboard.js\",\n                                                lineNumber: 266,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\erc-convert-next\\\\next\\\\src\\\\components\\\\Dashboard.js\",\n                                        lineNumber: 264,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"modern-card-body\",\n                                        children: stats.recentActivity.map((activity)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"activity-item\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: `activity-icon ${activity.color}`,\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                            className: `fas fa-${activity.icon}`\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\xampp\\\\htdocs\\\\erc-convert-next\\\\next\\\\src\\\\components\\\\Dashboard.js\",\n                                                            lineNumber: 274,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\erc-convert-next\\\\next\\\\src\\\\components\\\\Dashboard.js\",\n                                                        lineNumber: 273,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"activity-content\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"activity-title\",\n                                                                children: activity.title\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\xampp\\\\htdocs\\\\erc-convert-next\\\\next\\\\src\\\\components\\\\Dashboard.js\",\n                                                                lineNumber: 277,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"activity-subtitle\",\n                                                                children: activity.subtitle\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\xampp\\\\htdocs\\\\erc-convert-next\\\\next\\\\src\\\\components\\\\Dashboard.js\",\n                                                                lineNumber: 278,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\erc-convert-next\\\\next\\\\src\\\\components\\\\Dashboard.js\",\n                                                        lineNumber: 276,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"activity-time\",\n                                                        children: activity.time\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\erc-convert-next\\\\next\\\\src\\\\components\\\\Dashboard.js\",\n                                                        lineNumber: 280,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                ]\n                                            }, activity.id, true, {\n                                                fileName: \"C:\\\\xampp\\\\htdocs\\\\erc-convert-next\\\\next\\\\src\\\\components\\\\Dashboard.js\",\n                                                lineNumber: 272,\n                                                columnNumber: 21\n                                            }, undefined))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\erc-convert-next\\\\next\\\\src\\\\components\\\\Dashboard.js\",\n                                        lineNumber: 270,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\xampp\\\\htdocs\\\\erc-convert-next\\\\next\\\\src\\\\components\\\\Dashboard.js\",\n                                lineNumber: 263,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\xampp\\\\htdocs\\\\erc-convert-next\\\\next\\\\src\\\\components\\\\Dashboard.js\",\n                            lineNumber: 262,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\xampp\\\\htdocs\\\\erc-convert-next\\\\next\\\\src\\\\components\\\\Dashboard.js\",\n                        lineNumber: 261,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\xampp\\\\htdocs\\\\erc-convert-next\\\\next\\\\src\\\\components\\\\Dashboard.js\",\n                lineNumber: 145,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\xampp\\\\htdocs\\\\erc-convert-next\\\\next\\\\src\\\\components\\\\Dashboard.js\",\n            lineNumber: 144,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\xampp\\\\htdocs\\\\erc-convert-next\\\\next\\\\src\\\\components\\\\Dashboard.js\",\n        lineNumber: 143,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Dashboard);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Dashboard.js\n");

/***/ }),

/***/ "(ssr)/./src/components/Footer.js":
/*!**********************************!*\
  !*** ./src/components/Footer.js ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Footer)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nfunction Footer() {\n    const currentYear = new Date().getFullYear();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"footer_part\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container-fluid\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"row\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"col-lg-12\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"footer_iner text-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            children: [\n                                currentYear,\n                                \" \\xa9 Occams Portal -\",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: \"https://occamsadvisory.com\",\n                                    target: \"_blank\",\n                                    rel: \"noopener noreferrer\",\n                                    children: \"Occams Advisory\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\xampp\\\\htdocs\\\\erc-convert-next\\\\next\\\\src\\\\components\\\\Footer.js\",\n                                    lineNumber: 14,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\xampp\\\\htdocs\\\\erc-convert-next\\\\next\\\\src\\\\components\\\\Footer.js\",\n                            lineNumber: 12,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\xampp\\\\htdocs\\\\erc-convert-next\\\\next\\\\src\\\\components\\\\Footer.js\",\n                        lineNumber: 11,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\xampp\\\\htdocs\\\\erc-convert-next\\\\next\\\\src\\\\components\\\\Footer.js\",\n                    lineNumber: 10,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\xampp\\\\htdocs\\\\erc-convert-next\\\\next\\\\src\\\\components\\\\Footer.js\",\n                lineNumber: 9,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\xampp\\\\htdocs\\\\erc-convert-next\\\\next\\\\src\\\\components\\\\Footer.js\",\n            lineNumber: 8,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\xampp\\\\htdocs\\\\erc-convert-next\\\\next\\\\src\\\\components\\\\Footer.js\",\n        lineNumber: 7,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9Gb290ZXIuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUVlLFNBQVNBO0lBQ3RCLE1BQU1DLGNBQWMsSUFBSUMsT0FBT0MsV0FBVztJQUUxQyxxQkFDRSw4REFBQ0M7UUFBSUMsV0FBVTtrQkFDYiw0RUFBQ0Q7WUFBSUMsV0FBVTtzQkFDYiw0RUFBQ0Q7Z0JBQUlDLFdBQVU7MEJBQ2IsNEVBQUNEO29CQUFJQyxXQUFVOzhCQUNiLDRFQUFDRDt3QkFBSUMsV0FBVTtrQ0FDYiw0RUFBQ0M7O2dDQUNFTDtnQ0FBWTs4Q0FDYiw4REFBQ007b0NBQUVDLE1BQUs7b0NBQTZCQyxRQUFPO29DQUFTQyxLQUFJOzhDQUFzQjs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQVUvRiIsInNvdXJjZXMiOlsid2VicGFjazovL2VyYy1uZXh0Ly4vc3JjL2NvbXBvbmVudHMvRm9vdGVyLmpzPzlkZjAiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnO1xyXG5cclxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gRm9vdGVyKCkge1xyXG4gIGNvbnN0IGN1cnJlbnRZZWFyID0gbmV3IERhdGUoKS5nZXRGdWxsWWVhcigpO1xyXG5cclxuICByZXR1cm4gKFxyXG4gICAgPGRpdiBjbGFzc05hbWU9XCJmb290ZXJfcGFydFwiPlxyXG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImNvbnRhaW5lci1mbHVpZFwiPlxyXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicm93XCI+XHJcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImNvbC1sZy0xMlwiPlxyXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZvb3Rlcl9pbmVyIHRleHQtY2VudGVyXCI+XHJcbiAgICAgICAgICAgICAgPHA+XHJcbiAgICAgICAgICAgICAgICB7Y3VycmVudFllYXJ9IMKpIE9jY2FtcyBQb3J0YWwgLVxyXG4gICAgICAgICAgICAgICAgPGEgaHJlZj1cImh0dHBzOi8vb2NjYW1zYWR2aXNvcnkuY29tXCIgdGFyZ2V0PVwiX2JsYW5rXCIgcmVsPVwibm9vcGVuZXIgbm9yZWZlcnJlclwiPlxyXG4gICAgICAgICAgICAgICAgICBPY2NhbXMgQWR2aXNvcnlcclxuICAgICAgICAgICAgICAgIDwvYT5cclxuICAgICAgICAgICAgICA8L3A+XHJcbiAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgPC9kaXY+XHJcbiAgICAgIDwvZGl2PlxyXG4gICAgPC9kaXY+XHJcbiAgKTtcclxufSJdLCJuYW1lcyI6WyJGb290ZXIiLCJjdXJyZW50WWVhciIsIkRhdGUiLCJnZXRGdWxsWWVhciIsImRpdiIsImNsYXNzTmFtZSIsInAiLCJhIiwiaHJlZiIsInRhcmdldCIsInJlbCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Footer.js\n");

/***/ }),

/***/ "(ssr)/./src/components/Header.js":
/*!**********************************!*\
  !*** ./src/components/Header.js ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Header)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction Header() {\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Get user data from localStorage\n        if (false) {}\n    }, []);\n    const handleLogout = async ()=>{\n        try {\n            // Call logout API to clear cookie\n            await fetch(\"/api/auth/logout\", {\n                method: \"POST\"\n            });\n            // Clear localStorage\n            if (false) {}\n            // Redirect to login\n            window.location.href = \"/login\";\n        } catch (error) {\n            console.error(\"Logout error:\", error);\n            // Force redirect even if API call fails\n            window.location.href = \"/login\";\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"sidebar_icon d-lg-none\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                    className: \"ti-menu\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\xampp\\\\htdocs\\\\erc-convert-next\\\\next\\\\src\\\\components\\\\Header.js\",\n                    lineNumber: 44,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\xampp\\\\htdocs\\\\erc-convert-next\\\\next\\\\src\\\\components\\\\Header.js\",\n                lineNumber: 43,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"serach_field-area d-flex align-items-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"search_inner\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"search_field\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"text\",\n                                    placeholder: \"Search here...\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\xampp\\\\htdocs\\\\erc-convert-next\\\\next\\\\src\\\\components\\\\Header.js\",\n                                    lineNumber: 50,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\xampp\\\\htdocs\\\\erc-convert-next\\\\next\\\\src\\\\components\\\\Header.js\",\n                                lineNumber: 49,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"submit\",\n                                className: \"serach_button\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                    className: \"ti-search\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\xampp\\\\htdocs\\\\erc-convert-next\\\\next\\\\src\\\\components\\\\Header.js\",\n                                    lineNumber: 53,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\xampp\\\\htdocs\\\\erc-convert-next\\\\next\\\\src\\\\components\\\\Header.js\",\n                                lineNumber: 52,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\xampp\\\\htdocs\\\\erc-convert-next\\\\next\\\\src\\\\components\\\\Header.js\",\n                        lineNumber: 48,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\xampp\\\\htdocs\\\\erc-convert-next\\\\next\\\\src\\\\components\\\\Header.js\",\n                    lineNumber: 47,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\xampp\\\\htdocs\\\\erc-convert-next\\\\next\\\\src\\\\components\\\\Header.js\",\n                lineNumber: 46,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"header_right d-flex justify-content-between align-items-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"header_notification_warp d-flex align-items-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                            className: \"bell_notification_clicker\",\n                            href: \"#\",\n                            style: {\n                                textDecoration: \"none\"\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                    className: \"ti-bell\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\xampp\\\\htdocs\\\\erc-convert-next\\\\next\\\\src\\\\components\\\\Header.js\",\n                                    lineNumber: 61,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"notification_count\",\n                                    children: \"3\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\xampp\\\\htdocs\\\\erc-convert-next\\\\next\\\\src\\\\components\\\\Header.js\",\n                                    lineNumber: 62,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\xampp\\\\htdocs\\\\erc-convert-next\\\\next\\\\src\\\\components\\\\Header.js\",\n                            lineNumber: 60,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\xampp\\\\htdocs\\\\erc-convert-next\\\\next\\\\src\\\\components\\\\Header.js\",\n                        lineNumber: 59,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"profile_info\",\n                        onClick: ()=>{\n                            const details = document.querySelector(\".profile_info_details\");\n                            if (details) {\n                                details.style.display = details.style.display === \"block\" ? \"none\" : \"block\";\n                            }\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                src: \"/assets/images/logo.png\",\n                                alt: \"Profile\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\xampp\\\\htdocs\\\\erc-convert-next\\\\next\\\\src\\\\components\\\\Header.js\",\n                                lineNumber: 71,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"profile_info_iner\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"profile_author_name\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: user?.displayName || \"User\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\xampp\\\\htdocs\\\\erc-convert-next\\\\next\\\\src\\\\components\\\\Header.js\",\n                                                lineNumber: 74,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                children: user?.role || \"Member\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\xampp\\\\htdocs\\\\erc-convert-next\\\\next\\\\src\\\\components\\\\Header.js\",\n                                                lineNumber: 75,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\erc-convert-next\\\\next\\\\src\\\\components\\\\Header.js\",\n                                        lineNumber: 73,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"profile_info_details\",\n                                        style: {\n                                            display: \"none\"\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: \"/my-profile\",\n                                                children: \"My Profile\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\xampp\\\\htdocs\\\\erc-convert-next\\\\next\\\\src\\\\components\\\\Header.js\",\n                                                lineNumber: 78,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: \"/settings\",\n                                                children: \"Settings\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\xampp\\\\htdocs\\\\erc-convert-next\\\\next\\\\src\\\\components\\\\Header.js\",\n                                                lineNumber: 79,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: \"#\",\n                                                onClick: handleLogout,\n                                                children: \"Log Out\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\xampp\\\\htdocs\\\\erc-convert-next\\\\next\\\\src\\\\components\\\\Header.js\",\n                                                lineNumber: 80,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\erc-convert-next\\\\next\\\\src\\\\components\\\\Header.js\",\n                                        lineNumber: 77,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\xampp\\\\htdocs\\\\erc-convert-next\\\\next\\\\src\\\\components\\\\Header.js\",\n                                lineNumber: 72,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\xampp\\\\htdocs\\\\erc-convert-next\\\\next\\\\src\\\\components\\\\Header.js\",\n                        lineNumber: 65,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\xampp\\\\htdocs\\\\erc-convert-next\\\\next\\\\src\\\\components\\\\Header.js\",\n                lineNumber: 58,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Header.js\n");

/***/ }),

/***/ "(ssr)/./src/components/Sidebar.js":
/*!***********************************!*\
  !*** ./src/components/Sidebar.js ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Sidebar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nfunction Sidebar() {\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.usePathname)();\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isOpen, setIsOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Get user data from localStorage\n        if (false) {}\n        // Handle sidebar toggle\n        const handleSidebarToggle = ()=>{\n            setIsOpen(!isOpen);\n        };\n        // Add event listener for sidebar toggle\n        const sidebarIcon = document.querySelector(\".sidebar_icon\");\n        if (sidebarIcon) {\n            sidebarIcon.addEventListener(\"click\", handleSidebarToggle);\n        }\n        // Cleanup\n        return ()=>{\n            if (sidebarIcon) {\n                sidebarIcon.removeEventListener(\"click\", handleSidebarToggle);\n            }\n        };\n    }, [\n        isOpen\n    ]);\n    const menuItems = [\n        {\n            title: \"Dashboard\",\n            icon: \"ti-dashboard\",\n            path: \"/dashboard\",\n            active: pathname === \"/dashboard\"\n        },\n        {\n            title: \"Contacts\",\n            icon: \"ti-user\",\n            path: \"/contacts\",\n            active: pathname === \"/contacts\"\n        },\n        {\n            title: \"Create User\",\n            icon: \"ti-plus\",\n            path: \"/create-user\",\n            active: pathname === \"/create-user\"\n        },\n        {\n            title: \"Reports\",\n            icon: \"ti-bar-chart\",\n            path: \"/reports\",\n            active: pathname.startsWith(\"/reports\"),\n            submenu: [\n                {\n                    title: \"Lead Reports\",\n                    path: \"/reports/leads\"\n                },\n                {\n                    title: \"Sales Reports\",\n                    path: \"/reports/sales\"\n                },\n                {\n                    title: \"Analytics\",\n                    path: \"/reports/analytics\"\n                }\n            ]\n        },\n        {\n            title: \"Finance\",\n            icon: \"ti-money\",\n            path: \"/finance\",\n            active: pathname.startsWith(\"/finance\"),\n            submenu: [\n                {\n                    title: \"Invoices\",\n                    path: \"/finance/invoices\"\n                },\n                {\n                    title: \"Create Invoice\",\n                    path: \"/finance/create-invoice\"\n                }\n            ]\n        },\n        {\n            title: \"Send Lead\",\n            icon: \"ti-share\",\n            path: \"/send-lead\",\n            active: pathname === \"/send-lead\"\n        },\n        {\n            title: \"Settings\",\n            icon: \"ti-settings\",\n            path: \"/contact-settings\",\n            active: pathname === \"/contact-settings\"\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n        className: `sidebar ${isOpen ? \"active_sidebar\" : \"\"}`,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"logo d-flex justify-content-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                        className: \"large_logo\",\n                        href: \"/dashboard\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                            src: \"/assets/images/logo.png\",\n                            alt: \"Occams Portal\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\xampp\\\\htdocs\\\\erc-convert-next\\\\next\\\\src\\\\components\\\\Sidebar.js\",\n                            lineNumber: 98,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\xampp\\\\htdocs\\\\erc-convert-next\\\\next\\\\src\\\\components\\\\Sidebar.js\",\n                        lineNumber: 97,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                        className: \"small_logo\",\n                        href: \"/dashboard\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                            src: \"/assets/images/logo.png\",\n                            alt: \"Occams Portal\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\xampp\\\\htdocs\\\\erc-convert-next\\\\next\\\\src\\\\components\\\\Sidebar.js\",\n                            lineNumber: 101,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\xampp\\\\htdocs\\\\erc-convert-next\\\\next\\\\src\\\\components\\\\Sidebar.js\",\n                        lineNumber: 100,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"sidebar_close_icon d-lg-none\",\n                        onClick: ()=>setIsOpen(false),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                            className: \"ti-close\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\xampp\\\\htdocs\\\\erc-convert-next\\\\next\\\\src\\\\components\\\\Sidebar.js\",\n                            lineNumber: 104,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\xampp\\\\htdocs\\\\erc-convert-next\\\\next\\\\src\\\\components\\\\Sidebar.js\",\n                        lineNumber: 103,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\xampp\\\\htdocs\\\\erc-convert-next\\\\next\\\\src\\\\components\\\\Sidebar.js\",\n                lineNumber: 96,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                id: \"sidebar_menu\",\n                className: \"metismenu\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"nav_title\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                children: \"Main Menu\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\xampp\\\\htdocs\\\\erc-convert-next\\\\next\\\\src\\\\components\\\\Sidebar.js\",\n                                lineNumber: 111,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\xampp\\\\htdocs\\\\erc-convert-next\\\\next\\\\src\\\\components\\\\Sidebar.js\",\n                            lineNumber: 110,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\xampp\\\\htdocs\\\\erc-convert-next\\\\next\\\\src\\\\components\\\\Sidebar.js\",\n                        lineNumber: 109,\n                        columnNumber: 9\n                    }, this),\n                    menuItems.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                            className: item.active ? \"mm-active\" : \"\",\n                            children: item.submenu ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"#\",\n                                        className: item.active ? \"active\" : \"\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                className: item.icon\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\xampp\\\\htdocs\\\\erc-convert-next\\\\next\\\\src\\\\components\\\\Sidebar.js\",\n                                                lineNumber: 120,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: item.title\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\xampp\\\\htdocs\\\\erc-convert-next\\\\next\\\\src\\\\components\\\\Sidebar.js\",\n                                                lineNumber: 121,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\erc-convert-next\\\\next\\\\src\\\\components\\\\Sidebar.js\",\n                                        lineNumber: 119,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                        className: `mm-collapse ${item.active ? \"mm-show\" : \"\"}`,\n                                        children: item.submenu.map((subItem, subIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                    href: subItem.path,\n                                                    className: pathname === subItem.path ? \"active\" : \"\",\n                                                    children: subItem.title\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\xampp\\\\htdocs\\\\erc-convert-next\\\\next\\\\src\\\\components\\\\Sidebar.js\",\n                                                    lineNumber: 126,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, subIndex, false, {\n                                                fileName: \"C:\\\\xampp\\\\htdocs\\\\erc-convert-next\\\\next\\\\src\\\\components\\\\Sidebar.js\",\n                                                lineNumber: 125,\n                                                columnNumber: 21\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\erc-convert-next\\\\next\\\\src\\\\components\\\\Sidebar.js\",\n                                        lineNumber: 123,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                href: item.path,\n                                className: item.active ? \"active\" : \"\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                        className: item.icon\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\erc-convert-next\\\\next\\\\src\\\\components\\\\Sidebar.js\",\n                                        lineNumber: 135,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: item.title\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\erc-convert-next\\\\next\\\\src\\\\components\\\\Sidebar.js\",\n                                        lineNumber: 136,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\xampp\\\\htdocs\\\\erc-convert-next\\\\next\\\\src\\\\components\\\\Sidebar.js\",\n                                lineNumber: 134,\n                                columnNumber: 15\n                            }, this)\n                        }, index, false, {\n                            fileName: \"C:\\\\xampp\\\\htdocs\\\\erc-convert-next\\\\next\\\\src\\\\components\\\\Sidebar.js\",\n                            lineNumber: 116,\n                            columnNumber: 11\n                        }, this))\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\xampp\\\\htdocs\\\\erc-convert-next\\\\next\\\\src\\\\components\\\\Sidebar.js\",\n                lineNumber: 108,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\xampp\\\\htdocs\\\\erc-convert-next\\\\next\\\\src\\\\components\\\\Sidebar.js\",\n        lineNumber: 95,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Sidebar.js\n");

/***/ }),

/***/ "(rsc)/./src/assets/css/connect360-login.css":
/*!*********************************************!*\
  !*** ./src/assets/css/connect360-login.css ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"ce0d7d361907\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXNzZXRzL2Nzcy9jb25uZWN0MzYwLWxvZ2luLmNzcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsaUVBQWUsY0FBYztBQUM3QixJQUFJLEtBQVUsRUFBRSxFQUF1QiIsInNvdXJjZXMiOlsid2VicGFjazovL2VyYy1uZXh0Ly4vc3JjL2Fzc2V0cy9jc3MvY29ubmVjdDM2MC1sb2dpbi5jc3M/YmZhNSJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcImNlMGQ3ZDM2MTkwN1wiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/assets/css/connect360-login.css\n");

/***/ }),

/***/ "(rsc)/./src/assets/css/dashboard.css":
/*!**************************************!*\
  !*** ./src/assets/css/dashboard.css ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"04757f981904\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXNzZXRzL2Nzcy9kYXNoYm9hcmQuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZXJjLW5leHQvLi9zcmMvYXNzZXRzL2Nzcy9kYXNoYm9hcmQuY3NzPzU5MjEiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCIwNDc1N2Y5ODE5MDRcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/assets/css/dashboard.css\n");

/***/ }),

/***/ "(rsc)/./src/assets/css/design-enhancements.css":
/*!************************************************!*\
  !*** ./src/assets/css/design-enhancements.css ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"5f5dc5d693e8\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXNzZXRzL2Nzcy9kZXNpZ24tZW5oYW5jZW1lbnRzLmNzcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsaUVBQWUsY0FBYztBQUM3QixJQUFJLEtBQVUsRUFBRSxFQUF1QiIsInNvdXJjZXMiOlsid2VicGFjazovL2VyYy1uZXh0Ly4vc3JjL2Fzc2V0cy9jc3MvZGVzaWduLWVuaGFuY2VtZW50cy5jc3M/YTAzNiJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjVmNWRjNWQ2OTNlOFwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/assets/css/design-enhancements.css\n");

/***/ }),

/***/ "(rsc)/./src/assets/css/header-dropdown.css":
/*!********************************************!*\
  !*** ./src/assets/css/header-dropdown.css ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"2ad8992be0fd\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXNzZXRzL2Nzcy9oZWFkZXItZHJvcGRvd24uY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZXJjLW5leHQvLi9zcmMvYXNzZXRzL2Nzcy9oZWFkZXItZHJvcGRvd24uY3NzP2NiZDUiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCIyYWQ4OTkyYmUwZmRcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/assets/css/header-dropdown.css\n");

/***/ }),

/***/ "(rsc)/./src/assets/css/login-enhancements.css":
/*!***********************************************!*\
  !*** ./src/assets/css/login-enhancements.css ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"56bd7fb0f0b8\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXNzZXRzL2Nzcy9sb2dpbi1lbmhhbmNlbWVudHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZXJjLW5leHQvLi9zcmMvYXNzZXRzL2Nzcy9sb2dpbi1lbmhhbmNlbWVudHMuY3NzPzI2NmQiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCI1NmJkN2ZiMGYwYjhcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/assets/css/login-enhancements.css\n");

/***/ }),

/***/ "(rsc)/./src/assets/css/metisMenu.css":
/*!**************************************!*\
  !*** ./src/assets/css/metisMenu.css ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"106e07256cb7\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXNzZXRzL2Nzcy9tZXRpc01lbnUuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZXJjLW5leHQvLi9zcmMvYXNzZXRzL2Nzcy9tZXRpc01lbnUuY3NzPzgyNWQiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCIxMDZlMDcyNTZjYjdcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/assets/css/metisMenu.css\n");

/***/ }),

/***/ "(rsc)/./src/assets/css/responsive-enhancements.css":
/*!****************************************************!*\
  !*** ./src/assets/css/responsive-enhancements.css ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"f07427fa74fc\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXNzZXRzL2Nzcy9yZXNwb25zaXZlLWVuaGFuY2VtZW50cy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9lcmMtbmV4dC8uL3NyYy9hc3NldHMvY3NzL3Jlc3BvbnNpdmUtZW5oYW5jZW1lbnRzLmNzcz85OGE0Il0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiZjA3NDI3ZmE3NGZjXCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/assets/css/responsive-enhancements.css\n");

/***/ }),

/***/ "(rsc)/./src/assets/css/sidebar.css":
/*!************************************!*\
  !*** ./src/assets/css/sidebar.css ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"e403c9ec38aa\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXNzZXRzL2Nzcy9zaWRlYmFyLmNzcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsaUVBQWUsY0FBYztBQUM3QixJQUFJLEtBQVUsRUFBRSxFQUF1QiIsInNvdXJjZXMiOlsid2VicGFjazovL2VyYy1uZXh0Ly4vc3JjL2Fzc2V0cy9jc3Mvc2lkZWJhci5jc3M/MjkzMiJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcImU0MDNjOWVjMzhhYVwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/assets/css/sidebar.css\n");

/***/ }),

/***/ "(rsc)/./src/assets/css/style.css":
/*!**********************************!*\
  !*** ./src/assets/css/style.css ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"9bf79ae2b622\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXNzZXRzL2Nzcy9zdHlsZS5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9lcmMtbmV4dC8uL3NyYy9hc3NldHMvY3NzL3N0eWxlLmNzcz83YmQ4Il0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiOWJmNzlhZTJiNjIyXCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/assets/css/style.css\n");

/***/ }),

/***/ "(rsc)/./src/app/dashboard/page.js":
/*!***********************************!*\
  !*** ./src/app/dashboard/page.js ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DashboardPage),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_AppLayout__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/AppLayout */ \"(rsc)/./src/components/AppLayout.js\");\n/* harmony import */ var _components_Dashboard__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/Dashboard */ \"(rsc)/./src/components/Dashboard.js\");\n\n\n // Renamed to avoid conflict\nconst metadata = {\n    title: \"Dashboard - Occams Portal\"\n};\nfunction DashboardPage() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AppLayout__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Dashboard__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n            fileName: \"C:\\\\xampp\\\\htdocs\\\\erc-convert-next\\\\next\\\\src\\\\app\\\\dashboard\\\\page.js\",\n            lineNumber: 11,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\xampp\\\\htdocs\\\\erc-convert-next\\\\next\\\\src\\\\app\\\\dashboard\\\\page.js\",\n        lineNumber: 10,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2Rhc2hib2FyZC9wYWdlLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7QUFBK0M7QUFDUyxDQUFDLDRCQUE0QjtBQUU5RSxNQUFNRSxXQUFXO0lBQ3RCQyxPQUFPO0FBQ1QsRUFBRTtBQUVhLFNBQVNDO0lBQ3RCLHFCQUNFLDhEQUFDSiw2REFBU0E7a0JBQ1IsNEVBQUNDLDZEQUFrQkE7Ozs7Ozs7Ozs7QUFHekIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9lcmMtbmV4dC8uL3NyYy9hcHAvZGFzaGJvYXJkL3BhZ2UuanM/ZDJiOCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgQXBwTGF5b3V0IGZyb20gJ0AvY29tcG9uZW50cy9BcHBMYXlvdXQnO1xuaW1wb3J0IERhc2hib2FyZENvbXBvbmVudCBmcm9tICdAL2NvbXBvbmVudHMvRGFzaGJvYXJkJzsgLy8gUmVuYW1lZCB0byBhdm9pZCBjb25mbGljdFxuXG5leHBvcnQgY29uc3QgbWV0YWRhdGEgPSB7XG4gIHRpdGxlOiAnRGFzaGJvYXJkIC0gT2NjYW1zIFBvcnRhbCcsXG59O1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBEYXNoYm9hcmRQYWdlKCkgeyAvLyBSZW5hbWVkIGZ1bmN0aW9uXG4gIHJldHVybiAoXG4gICAgPEFwcExheW91dD5cbiAgICAgIDxEYXNoYm9hcmRDb21wb25lbnQgLz5cbiAgICA8L0FwcExheW91dD5cbiAgKTtcbn1cblxuXG4iXSwibmFtZXMiOlsiQXBwTGF5b3V0IiwiRGFzaGJvYXJkQ29tcG9uZW50IiwibWV0YWRhdGEiLCJ0aXRsZSIsIkRhc2hib2FyZFBhZ2UiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/dashboard/page.js\n");

/***/ }),

/***/ "(rsc)/./src/app/error.js":
/*!**************************!*\
  !*** ./src/app/error.js ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\xampp\htdocs\erc-convert-next\next\src\app\error.js`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ }),

/***/ "(rsc)/./src/app/layout.js":
/*!***************************!*\
  !*** ./src/app/layout.js ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_js_import_Inter_arguments_subsets_latin_variable_font_inter_variableName_inter___WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.js\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"],\"variable\":\"--font-inter\"}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.js\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"],\\\"variable\\\":\\\"--font-inter\\\"}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_js_import_Inter_arguments_subsets_latin_variable_font_inter_variableName_inter___WEBPACK_IMPORTED_MODULE_12___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_js_import_Inter_arguments_subsets_latin_variable_font_inter_variableName_inter___WEBPACK_IMPORTED_MODULE_12__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_js_import_Mulish_arguments_subsets_latin_weight_200_300_400_500_600_700_800_900_variable_font_mulish_variableName_mulish___WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.js\",\"import\":\"Mulish\",\"arguments\":[{\"subsets\":[\"latin\"],\"weight\":[\"200\",\"300\",\"400\",\"500\",\"600\",\"700\",\"800\",\"900\"],\"variable\":\"--font-mulish\"}],\"variableName\":\"mulish\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.js\\\",\\\"import\\\":\\\"Mulish\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"],\\\"weight\\\":[\\\"200\\\",\\\"300\\\",\\\"400\\\",\\\"500\\\",\\\"600\\\",\\\"700\\\",\\\"800\\\",\\\"900\\\"],\\\"variable\\\":\\\"--font-mulish\\\"}],\\\"variableName\\\":\\\"mulish\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_js_import_Mulish_arguments_subsets_latin_weight_200_300_400_500_600_700_800_900_variable_font_mulish_variableName_mulish___WEBPACK_IMPORTED_MODULE_13___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_js_import_Mulish_arguments_subsets_latin_weight_200_300_400_500_600_700_800_900_variable_font_mulish_variableName_mulish___WEBPACK_IMPORTED_MODULE_13__);\n/* harmony import */ var bootstrap_dist_css_bootstrap_min_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! bootstrap/dist/css/bootstrap.min.css */ \"(rsc)/./node_modules/bootstrap/dist/css/bootstrap.min.css\");\n/* harmony import */ var _fortawesome_fontawesome_free_css_all_min_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @fortawesome/fontawesome-free/css/all.min.css */ \"(rsc)/./node_modules/@fortawesome/fontawesome-free/css/all.min.css\");\n/* harmony import */ var _assets_css_style_css__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../assets/css/style.css */ \"(rsc)/./src/assets/css/style.css\");\n/* harmony import */ var _assets_css_sidebar_css__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../assets/css/sidebar.css */ \"(rsc)/./src/assets/css/sidebar.css\");\n/* harmony import */ var _assets_css_header_dropdown_css__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../assets/css/header-dropdown.css */ \"(rsc)/./src/assets/css/header-dropdown.css\");\n/* harmony import */ var _assets_css_metisMenu_css__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../assets/css/metisMenu.css */ \"(rsc)/./src/assets/css/metisMenu.css\");\n/* harmony import */ var _assets_css_design_enhancements_css__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../assets/css/design-enhancements.css */ \"(rsc)/./src/assets/css/design-enhancements.css\");\n/* harmony import */ var _assets_css_dashboard_css__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../assets/css/dashboard.css */ \"(rsc)/./src/assets/css/dashboard.css\");\n/* harmony import */ var _assets_css_login_enhancements_css__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../assets/css/login-enhancements.css */ \"(rsc)/./src/assets/css/login-enhancements.css\");\n/* harmony import */ var _assets_css_responsive_enhancements_css__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ../assets/css/responsive-enhancements.css */ \"(rsc)/./src/assets/css/responsive-enhancements.css\");\n/* harmony import */ var _assets_css_connect360_login_css__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ../assets/css/connect360-login.css */ \"(rsc)/./src/assets/css/connect360-login.css\");\n\n\n\n// Import CSS files\n\n\n// Import global CSS files\n\n\n\n\n\n\n\n\n\nconst metadata = {\n    title: \"Occams Portal\",\n    description: \"Occams Portal Application\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"head\", {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                    rel: \"stylesheet\",\n                    href: \"https://cdn.jsdelivr.net/npm/themify-icons@0.1.2/css/themify-icons.css\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\xampp\\\\htdocs\\\\erc-convert-next\\\\next\\\\src\\\\app\\\\layout.js\",\n                    lineNumber: 45,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\xampp\\\\htdocs\\\\erc-convert-next\\\\next\\\\src\\\\app\\\\layout.js\",\n                lineNumber: 37,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                className: `${(next_font_google_target_css_path_src_app_layout_js_import_Inter_arguments_subsets_latin_variable_font_inter_variableName_inter___WEBPACK_IMPORTED_MODULE_12___default().variable)} ${(next_font_google_target_css_path_src_app_layout_js_import_Mulish_arguments_subsets_latin_weight_200_300_400_500_600_700_800_900_variable_font_mulish_variableName_mulish___WEBPACK_IMPORTED_MODULE_13___default().variable)}`,\n                children: children\n            }, void 0, false, {\n                fileName: \"C:\\\\xampp\\\\htdocs\\\\erc-convert-next\\\\next\\\\src\\\\app\\\\layout.js\",\n                lineNumber: 50,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\xampp\\\\htdocs\\\\erc-convert-next\\\\next\\\\src\\\\app\\\\layout.js\",\n        lineNumber: 36,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.js\n");

/***/ }),

/***/ "(rsc)/./src/app/loading.js":
/*!****************************!*\
  !*** ./src/app/loading.js ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Loading)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n\nfunction Loading() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"d-flex justify-content-center align-items-center vh-100\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"spinner-border text-primary\",\n            role: \"status\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"visually-hidden\",\n                children: \"Loading...\"\n            }, void 0, false, {\n                fileName: \"C:\\\\xampp\\\\htdocs\\\\erc-convert-next\\\\next\\\\src\\\\app\\\\loading.js\",\n                lineNumber: 5,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\xampp\\\\htdocs\\\\erc-convert-next\\\\next\\\\src\\\\app\\\\loading.js\",\n            lineNumber: 4,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\xampp\\\\htdocs\\\\erc-convert-next\\\\next\\\\src\\\\app\\\\loading.js\",\n        lineNumber: 3,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2xvYWRpbmcuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUFlLFNBQVNBO0lBQ3RCLHFCQUNFLDhEQUFDQztRQUFJQyxXQUFVO2tCQUNiLDRFQUFDRDtZQUFJQyxXQUFVO1lBQThCQyxNQUFLO3NCQUNoRCw0RUFBQ0M7Z0JBQUtGLFdBQVU7MEJBQWtCOzs7Ozs7Ozs7Ozs7Ozs7O0FBSTFDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZXJjLW5leHQvLi9zcmMvYXBwL2xvYWRpbmcuanM/YmUxZCJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBMb2FkaW5nKCkge1xuICByZXR1cm4gKFxuICAgIDxkaXYgY2xhc3NOYW1lPVwiZC1mbGV4IGp1c3RpZnktY29udGVudC1jZW50ZXIgYWxpZ24taXRlbXMtY2VudGVyIHZoLTEwMFwiPlxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGlubmVyLWJvcmRlciB0ZXh0LXByaW1hcnlcIiByb2xlPVwic3RhdHVzXCI+XG4gICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInZpc3VhbGx5LWhpZGRlblwiPkxvYWRpbmcuLi48L3NwYW4+XG4gICAgICA8L2Rpdj5cbiAgICA8L2Rpdj5cbiAgKTtcbn1cbiJdLCJuYW1lcyI6WyJMb2FkaW5nIiwiZGl2IiwiY2xhc3NOYW1lIiwicm9sZSIsInNwYW4iXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/loading.js\n");

/***/ }),

/***/ "(rsc)/./src/app/not-found.js":
/*!******************************!*\
  !*** ./src/app/not-found.js ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ NotFound)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(rsc)/./node_modules/next/dist/api/link.js\");\n\n\nfunction NotFound() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"d-flex flex-column justify-content-center align-items-center vh-100\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                className: \"mb-4\",\n                children: \"404 - Page Not Found\"\n            }, void 0, false, {\n                fileName: \"C:\\\\xampp\\\\htdocs\\\\erc-convert-next\\\\next\\\\src\\\\app\\\\not-found.js\",\n                lineNumber: 6,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"mb-4\",\n                children: \"The page you are looking for does not exist.\"\n            }, void 0, false, {\n                fileName: \"C:\\\\xampp\\\\htdocs\\\\erc-convert-next\\\\next\\\\src\\\\app\\\\not-found.js\",\n                lineNumber: 7,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                href: \"/dashboard\",\n                className: \"btn btn-primary\",\n                children: \"Return to Dashboard\"\n            }, void 0, false, {\n                fileName: \"C:\\\\xampp\\\\htdocs\\\\erc-convert-next\\\\next\\\\src\\\\app\\\\not-found.js\",\n                lineNumber: 8,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\xampp\\\\htdocs\\\\erc-convert-next\\\\next\\\\src\\\\app\\\\not-found.js\",\n        lineNumber: 5,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL25vdC1mb3VuZC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7OztBQUE2QjtBQUVkLFNBQVNDO0lBQ3RCLHFCQUNFLDhEQUFDQztRQUFJQyxXQUFVOzswQkFDYiw4REFBQ0M7Z0JBQUdELFdBQVU7MEJBQU87Ozs7OzswQkFDckIsOERBQUNFO2dCQUFFRixXQUFVOzBCQUFPOzs7Ozs7MEJBQ3BCLDhEQUFDSCxpREFBSUE7Z0JBQUNNLE1BQUs7Z0JBQWFILFdBQVU7MEJBQWtCOzs7Ozs7Ozs7Ozs7QUFLMUQiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9lcmMtbmV4dC8uL3NyYy9hcHAvbm90LWZvdW5kLmpzPzg3NjQiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IExpbmsgZnJvbSAnbmV4dC9saW5rJztcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gTm90Rm91bmQoKSB7XG4gIHJldHVybiAoXG4gICAgPGRpdiBjbGFzc05hbWU9XCJkLWZsZXggZmxleC1jb2x1bW4ganVzdGlmeS1jb250ZW50LWNlbnRlciBhbGlnbi1pdGVtcy1jZW50ZXIgdmgtMTAwXCI+XG4gICAgICA8aDEgY2xhc3NOYW1lPVwibWItNFwiPjQwNCAtIFBhZ2UgTm90IEZvdW5kPC9oMT5cbiAgICAgIDxwIGNsYXNzTmFtZT1cIm1iLTRcIj5UaGUgcGFnZSB5b3UgYXJlIGxvb2tpbmcgZm9yIGRvZXMgbm90IGV4aXN0LjwvcD5cbiAgICAgIDxMaW5rIGhyZWY9XCIvZGFzaGJvYXJkXCIgY2xhc3NOYW1lPVwiYnRuIGJ0bi1wcmltYXJ5XCI+XG4gICAgICAgIFJldHVybiB0byBEYXNoYm9hcmRcbiAgICAgIDwvTGluaz5cbiAgICA8L2Rpdj5cbiAgKTtcbn1cbiJdLCJuYW1lcyI6WyJMaW5rIiwiTm90Rm91bmQiLCJkaXYiLCJjbGFzc05hbWUiLCJoMSIsInAiLCJocmVmIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/not-found.js\n");

/***/ }),

/***/ "(rsc)/./src/components/AppLayout.js":
/*!*************************************!*\
  !*** ./src/components/AppLayout.js ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\xampp\htdocs\erc-convert-next\next\src\components\AppLayout.js`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ }),

/***/ "(rsc)/./src/components/Dashboard.js":
/*!*************************************!*\
  !*** ./src/components/Dashboard.js ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\xampp\htdocs\erc-convert-next\next\src\components\Dashboard.js`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__":
/*!**************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ ***!
  \**************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/lib/metadata/get-metadata-route */ \"(rsc)/./node_modules/next/dist/lib/metadata/get-metadata-route.js\");\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__);\n  \n\n  /* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((props) => {\n    const imageData = {\"type\":\"image/x-icon\",\"sizes\":\"16x16\"}\n    const imageUrl = (0,next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__.fillMetadataSegment)(\".\", props.params, \"favicon.ico\")\n\n    return [{\n      ...imageData,\n      url: imageUrl + \"\",\n    }]\n  });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LW1ldGFkYXRhLWltYWdlLWxvYWRlci5qcz90eXBlPWZhdmljb24mc2VnbWVudD0mYmFzZVBhdGg9JnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMhLi9zcmMvYXBwL2Zhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFBLEVBQWlGOztBQUVqRixFQUFFLGlFQUFlO0FBQ2pCLHVCQUF1QjtBQUN2QixxQkFBcUIsOEZBQW1COztBQUV4QztBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9lcmMtbmV4dC8uL3NyYy9hcHAvZmF2aWNvbi5pY28/MDYxZSJdLCJzb3VyY2VzQ29udGVudCI6WyIgIGltcG9ydCB7IGZpbGxNZXRhZGF0YVNlZ21lbnQgfSBmcm9tICduZXh0L2Rpc3QvbGliL21ldGFkYXRhL2dldC1tZXRhZGF0YS1yb3V0ZSdcblxuICBleHBvcnQgZGVmYXVsdCAocHJvcHMpID0+IHtcbiAgICBjb25zdCBpbWFnZURhdGEgPSB7XCJ0eXBlXCI6XCJpbWFnZS94LWljb25cIixcInNpemVzXCI6XCIxNngxNlwifVxuICAgIGNvbnN0IGltYWdlVXJsID0gZmlsbE1ldGFkYXRhU2VnbWVudChcIi5cIiwgcHJvcHMucGFyYW1zLCBcImZhdmljb24uaWNvXCIpXG5cbiAgICByZXR1cm4gW3tcbiAgICAgIC4uLmltYWdlRGF0YSxcbiAgICAgIHVybDogaW1hZ2VVcmwgKyBcIlwiLFxuICAgIH1dXG4gIH0iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/bootstrap","vendor-chunks/@fortawesome","vendor-chunks/chart.js","vendor-chunks/react-chartjs-2","vendor-chunks/@kurkle"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fdashboard%2Fpage&page=%2Fdashboard%2Fpage&appPaths=%2Fdashboard%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fpage.js&appDir=C%3A%5Cxampp%5Chtdocs%5Cerc-convert-next%5Cnext%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Cxampp%5Chtdocs%5Cerc-convert-next%5Cnext&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();