/* ===== DASHBOARD SPECIFIC STYLES ===== */

/* Dashboard Container */
.dashboard-container {
  max-width: 1400px;
  margin: 0 auto;
}

/* Dashboard Header */
.dashboard-header {
  margin-bottom: 2rem;
  text-align: center;
  padding: 2rem 0;
  background: linear-gradient(135deg, var(--bg-secondary) 0%, var(--bg-tertiary) 100%);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-md);
}

.dashboard-title {
  font-size: 2.5rem;
  font-weight: 800;
  color: var(--text-primary);
  margin: 0 0 0.5rem 0;
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.dashboard-subtitle {
  font-size: 1.125rem;
  color: var(--text-secondary);
  margin: 0;
  font-weight: 500;
}

/* Statistics Grid */
.stat-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.stat-card {
  background: var(--bg-secondary);
  padding: 2rem;
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-md);
  border: 1px solid var(--border-light);
  text-align: center;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
}

.stat-card:hover {
  transform: translateY(-8px);
  box-shadow: var(--shadow-xl);
}

.stat-card-icon {
  width: 64px;
  height: 64px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 1rem;
  font-size: 1.5rem;
  color: white;
  position: relative;
}

.stat-card-icon.bg-purple {
  background: linear-gradient(135deg, #8b5cf6, #a855f7);
}

.stat-card-icon.bg-blue {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
}

.stat-card-icon.bg-green {
  background: linear-gradient(135deg, #10b981, #059669);
}

.stat-card-icon.bg-orange {
  background: linear-gradient(135deg, #f59e0b, #d97706);
}

.stat-card-value {
  font-size: 2.5rem;
  font-weight: 800;
  color: var(--text-primary);
  margin-bottom: 0.5rem;
  line-height: 1;
}

.stat-card-label {
  font-size: 0.875rem;
  color: var(--text-secondary);
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  margin: 0;
}

/* Modern Card Styling */
.modern-card {
  background: var(--bg-secondary);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-md);
  border: 1px solid var(--border-light);
  overflow: hidden;
  transition: all 0.3s ease;
  height: 100%;
}

.modern-card:hover {
  box-shadow: var(--shadow-lg);
}

.modern-card-header {
  padding: 1.5rem;
  border-bottom: 1px solid var(--border-color);
  background: linear-gradient(135deg, var(--bg-secondary) 0%, var(--bg-tertiary) 100%);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.modern-card-title {
  font-size: 1.25rem;
  font-weight: 700;
  color: var(--text-primary);
  margin: 0;
}

.modern-card-body {
  padding: 1.5rem;
}

/* Chart Container */
.chart-container {
  height: 400px;
  position: relative;
}

.chart-type-selector {
  display: flex;
  gap: 0.5rem;
}

.chart-type-selector .btn {
  padding: 0.5rem 1rem;
  font-size: 0.875rem;
  border-radius: var(--radius-md);
}

/* Support Section */
.support-section .modern-card {
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
}

.support-header {
  display: flex;
  align-items: center;
  margin-bottom: 1.5rem;
  padding: 1rem;
  background: var(--bg-secondary);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
}

.support-header-icon {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  background: linear-gradient(135deg, var(--info-color), #0056b3);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.25rem;
  margin-right: 1rem;
}

.support-header-content h3 {
  font-size: 1.125rem;
  font-weight: 700;
  color: var(--text-primary);
  margin: 0 0 0.25rem 0;
}

.support-header-content p {
  font-size: 0.875rem;
  color: var(--text-secondary);
  margin: 0;
}

.support-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  width: 100%;
  padding: 0.75rem;
  background: linear-gradient(135deg, var(--success-color), #28a745);
  color: white;
  text-decoration: none;
  border-radius: var(--radius-lg);
  font-weight: 600;
  transition: all 0.2s ease;
  margin-bottom: 1.5rem;
}

.support-btn:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
  color: white;
}

/* Ticket List */
.ticket-list {
  space-y: 0.75rem;
}

.ticket-item {
  display: flex;
  align-items: flex-start;
  padding: 1rem;
  background: var(--bg-secondary);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
  border-left: 4px solid transparent;
  margin-bottom: 0.75rem;
}

.ticket-priority {
  width: 4px;
  height: 100%;
  border-radius: 2px;
  margin-right: 1rem;
  flex-shrink: 0;
}

.ticket-priority.priority-high {
  background: var(--danger-color);
}

.ticket-priority.priority-medium {
  background: var(--warning-color);
}

.ticket-content {
  flex: 1;
}

.ticket-title {
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 0.5rem;
  font-size: 0.875rem;
}

.ticket-status {
  font-size: 0.75rem;
  color: var(--text-secondary);
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.ticket-badge {
  padding: 0.25rem 0.5rem;
  border-radius: var(--radius-sm);
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.025em;
}

.ticket-badge.badge-open {
  background: rgba(255, 59, 48, 0.1);
  color: var(--danger-color);
}

.ticket-badge.badge-progress {
  background: rgba(255, 149, 0, 0.1);
  color: var(--warning-color);
}

/* Activity Feed */
.activity-feed .modern-card {
  background: var(--bg-secondary);
}

.activity-item {
  display: flex;
  align-items: center;
  padding: 1rem 0;
  border-bottom: 1px solid var(--border-light);
}

.activity-item:last-child {
  border-bottom: none;
}

.activity-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 0.875rem;
  margin-right: 1rem;
  flex-shrink: 0;
}

.activity-icon.bg-purple {
  background: linear-gradient(135deg, #8b5cf6, #a855f7);
}

.activity-icon.bg-green {
  background: linear-gradient(135deg, #10b981, #059669);
}

.activity-icon.bg-blue {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
}

.activity-icon.bg-orange {
  background: linear-gradient(135deg, #f59e0b, #d97706);
}

.activity-content {
  flex: 1;
}

.activity-title {
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 0.25rem 0;
}

.activity-subtitle {
  font-size: 0.75rem;
  color: var(--text-secondary);
  margin: 0;
}

.activity-time {
  font-size: 0.75rem;
  color: var(--text-muted);
  font-weight: 500;
}

/* Responsive Design */
@media (max-width: 768px) {
  .dashboard-title {
    font-size: 2rem;
  }
  
  .dashboard-subtitle {
    font-size: 1rem;
  }
  
  .stat-grid {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
  }
  
  .stat-card {
    padding: 1.5rem;
  }
  
  .stat-card-value {
    font-size: 2rem;
  }
  
  .chart-container {
    height: 300px;
  }
  
  .modern-card-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }
  
  .chart-type-selector {
    width: 100%;
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .dashboard-header {
    padding: 1.5rem 1rem;
  }
  
  .stat-grid {
    grid-template-columns: 1fr;
  }
  
  .activity-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.75rem;
  }
  
  .activity-time {
    align-self: flex-end;
  }
}
