'use client';

import { createGlobalStyle } from 'styled-components';

const GlobalStyles = createGlobalStyle`
  /* Ensure the lead report table displays at full width with proper scrolling */
  body {
    overflow-x: hidden !important;
    background-color: #f0f4f8 !important;
  }

  #wpwrap {
    width: 100%;
    overflow: hidden;
  }

  #wpcontent {
    width: calc(100% - 270px) !important;
    overflow: hidden !important;
  }

  #wpbody {
    width: 100%;
    padding: 0 !important;
    overflow: hidden;
  }

  #wpbody-content {
    width: 100%;
    overflow: hidden;
  }

  .lead-report-container {
    width: 100%;
    overflow: hidden;
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  }

  .lead-report-content {
    width: 100%;
    overflow: hidden;
  }

  .table-responsive {
    width: 100%;
    overflow-x: auto !important;
    display: block !important;
    position: relative;
    max-height: none !important;
    border: none !important;
  }

  .lead-report-table {
    table-layout: fixed !important;
    width: 100% !important;
    min-width: 1200px !important;
    margin-bottom: 0 !important;
  }

  /* Fix for Bootstrap container */
  .container-fluid {
    padding-right: 15px !important;
    padding-left: 15px !important;
    width: 100% !important;
    max-width: 100% !important;
    overflow: hidden !important;
  }

  /* Ensure the dashboard content area takes full width */
  .dashboard-content {
    width: 100%;
    max-width: 100%;
    padding: 0;
    overflow: hidden;
  }

  /* Fix for table columns */
  .lead-report-table th,
  .lead-report-table td {
    white-space: nowrap;
  }

  /* Action buttons at the top */
  .action-buttons-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    padding: 15px 0;
  }

  .btn-action {
    background-color: #ff5722;
    color: white;
    border: none;
    border-radius: 4px;
    padding: 10px 20px;
    font-weight: 500;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .btn-action:hover {
    background-color: #e64a19;
    color: white;
  }

  .welcome-message {
    color: #1e5799;
    font-weight: 500;
    font-size: 16px;
    display: flex;
    align-items: center;
  }
`;

export default GlobalStyles;
