"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/create-user/page",{

/***/ "(app-pages-browser)/./src/components/Sidebar.js":
/*!***********************************!*\
  !*** ./src/components/Sidebar.js ***!
  \***********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Sidebar; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction Sidebar() {\n    _s();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.usePathname)();\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isOpen, setIsOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Get user data from localStorage\n        if (true) {\n            const userData = localStorage.getItem(\"user\");\n            if (userData) {\n                setUser(JSON.parse(userData));\n            }\n        }\n        // Handle sidebar toggle\n        const handleSidebarToggle = ()=>{\n            setIsOpen(!isOpen);\n        };\n        // Add event listener for sidebar toggle\n        const sidebarIcon = document.querySelector(\".sidebar_icon\");\n        if (sidebarIcon) {\n            sidebarIcon.addEventListener(\"click\", handleSidebarToggle);\n        }\n        // Cleanup\n        return ()=>{\n            if (sidebarIcon) {\n                sidebarIcon.removeEventListener(\"click\", handleSidebarToggle);\n            }\n        };\n    }, [\n        isOpen\n    ]);\n    const menuItems = [\n        {\n            title: \"Dashboard\",\n            icon: \"ti-dashboard\",\n            path: \"/dashboard\",\n            active: pathname === \"/dashboard\"\n        },\n        {\n            title: \"Contacts\",\n            icon: \"ti-user\",\n            path: \"/contacts\",\n            active: pathname === \"/contacts\"\n        },\n        {\n            title: \"Create User\",\n            icon: \"ti-plus\",\n            path: \"/create-user\",\n            active: pathname === \"/create-user\"\n        },\n        {\n            title: \"Reports\",\n            icon: \"ti-bar-chart\",\n            path: \"/reports\",\n            active: pathname.startsWith(\"/reports\"),\n            submenu: [\n                {\n                    title: \"Lead Reports\",\n                    path: \"/reports/leads\"\n                },\n                {\n                    title: \"Sales Reports\",\n                    path: \"/reports/sales\"\n                },\n                {\n                    title: \"Analytics\",\n                    path: \"/reports/analytics\"\n                }\n            ]\n        },\n        {\n            title: \"Finance\",\n            icon: \"ti-money\",\n            path: \"/finance\",\n            active: pathname.startsWith(\"/finance\"),\n            submenu: [\n                {\n                    title: \"Invoices\",\n                    path: \"/finance/invoices\"\n                },\n                {\n                    title: \"Create Invoice\",\n                    path: \"/finance/create-invoice\"\n                }\n            ]\n        },\n        {\n            title: \"Send Lead\",\n            icon: \"ti-share\",\n            path: \"/send-lead\",\n            active: pathname === \"/send-lead\"\n        },\n        {\n            title: \"Settings\",\n            icon: \"ti-settings\",\n            path: \"/contact-settings\",\n            active: pathname === \"/contact-settings\"\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n        className: \"sidebar\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"logo d-flex justify-content-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                        className: \"large_logo\",\n                        href: \"/dashboard\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                            src: \"/assets/images/logo.png\",\n                            alt: \"Occams Portal\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\xampp\\\\htdocs\\\\erc-convert-next\\\\next\\\\src\\\\components\\\\Sidebar.js\",\n                            lineNumber: 98,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\xampp\\\\htdocs\\\\erc-convert-next\\\\next\\\\src\\\\components\\\\Sidebar.js\",\n                        lineNumber: 97,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                        className: \"small_logo\",\n                        href: \"/dashboard\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                            src: \"/assets/images/logo.png\",\n                            alt: \"Occams Portal\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\xampp\\\\htdocs\\\\erc-convert-next\\\\next\\\\src\\\\components\\\\Sidebar.js\",\n                            lineNumber: 101,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\xampp\\\\htdocs\\\\erc-convert-next\\\\next\\\\src\\\\components\\\\Sidebar.js\",\n                        lineNumber: 100,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"sidebar_close_icon d-lg-none\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                            className: \"ti-close\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\xampp\\\\htdocs\\\\erc-convert-next\\\\next\\\\src\\\\components\\\\Sidebar.js\",\n                            lineNumber: 104,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\xampp\\\\htdocs\\\\erc-convert-next\\\\next\\\\src\\\\components\\\\Sidebar.js\",\n                        lineNumber: 103,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\xampp\\\\htdocs\\\\erc-convert-next\\\\next\\\\src\\\\components\\\\Sidebar.js\",\n                lineNumber: 96,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                id: \"sidebar_menu\",\n                className: \"metismenu\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"nav_title\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                children: \"Main Menu\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\xampp\\\\htdocs\\\\erc-convert-next\\\\next\\\\src\\\\components\\\\Sidebar.js\",\n                                lineNumber: 111,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\xampp\\\\htdocs\\\\erc-convert-next\\\\next\\\\src\\\\components\\\\Sidebar.js\",\n                            lineNumber: 110,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\xampp\\\\htdocs\\\\erc-convert-next\\\\next\\\\src\\\\components\\\\Sidebar.js\",\n                        lineNumber: 109,\n                        columnNumber: 9\n                    }, this),\n                    menuItems.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                            className: item.active ? \"mm-active\" : \"\",\n                            children: item.submenu ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"#\",\n                                        className: item.active ? \"active\" : \"\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                className: item.icon\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\xampp\\\\htdocs\\\\erc-convert-next\\\\next\\\\src\\\\components\\\\Sidebar.js\",\n                                                lineNumber: 120,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: item.title\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\xampp\\\\htdocs\\\\erc-convert-next\\\\next\\\\src\\\\components\\\\Sidebar.js\",\n                                                lineNumber: 121,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\erc-convert-next\\\\next\\\\src\\\\components\\\\Sidebar.js\",\n                                        lineNumber: 119,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                        className: \"mm-collapse \".concat(item.active ? \"mm-show\" : \"\"),\n                                        children: item.submenu.map((subItem, subIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                    href: subItem.path,\n                                                    className: pathname === subItem.path ? \"active\" : \"\",\n                                                    children: subItem.title\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\xampp\\\\htdocs\\\\erc-convert-next\\\\next\\\\src\\\\components\\\\Sidebar.js\",\n                                                    lineNumber: 126,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, subIndex, false, {\n                                                fileName: \"C:\\\\xampp\\\\htdocs\\\\erc-convert-next\\\\next\\\\src\\\\components\\\\Sidebar.js\",\n                                                lineNumber: 125,\n                                                columnNumber: 21\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\erc-convert-next\\\\next\\\\src\\\\components\\\\Sidebar.js\",\n                                        lineNumber: 123,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                href: item.path,\n                                className: item.active ? \"active\" : \"\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                        className: item.icon\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\erc-convert-next\\\\next\\\\src\\\\components\\\\Sidebar.js\",\n                                        lineNumber: 135,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: item.title\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\erc-convert-next\\\\next\\\\src\\\\components\\\\Sidebar.js\",\n                                        lineNumber: 136,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\xampp\\\\htdocs\\\\erc-convert-next\\\\next\\\\src\\\\components\\\\Sidebar.js\",\n                                lineNumber: 134,\n                                columnNumber: 15\n                            }, this)\n                        }, index, false, {\n                            fileName: \"C:\\\\xampp\\\\htdocs\\\\erc-convert-next\\\\next\\\\src\\\\components\\\\Sidebar.js\",\n                            lineNumber: 116,\n                            columnNumber: 11\n                        }, this))\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\xampp\\\\htdocs\\\\erc-convert-next\\\\next\\\\src\\\\components\\\\Sidebar.js\",\n                lineNumber: 108,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\xampp\\\\htdocs\\\\erc-convert-next\\\\next\\\\src\\\\components\\\\Sidebar.js\",\n        lineNumber: 95,\n        columnNumber: 5\n    }, this);\n}\n_s(Sidebar, \"F6Da1BVOm/7IJLu23/5IIaurToE=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_3__.usePathname\n    ];\n});\n_c = Sidebar;\nvar _c;\n$RefreshReg$(_c, \"Sidebar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/Sidebar.js\n"));

/***/ })

});