import AppLayout from '@/components/AppLayout';

export const metadata = {
  title: 'Invoices - Occams Portal',
  description: 'Manage invoices',
};

export default function InvoicesPage() {
  return (
    <AppLayout>
      <div className="main_content_iner">
        <div className="container-fluid p-0">
          <div className="dashboard-container">
            <div className="dashboard-header">
              <h1 className="dashboard-title">Invoices</h1>
              <p className="dashboard-subtitle">Manage your invoices</p>
            </div>
            
            {/* Invoices content will go here */}
            <div className="modern-card">
              <div className="modern-card-header">
                <h3 className="modern-card-title">Invoice List</h3>
              </div>
              <div className="modern-card-body">
                <p>This page is under construction.</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </AppLayout>
  );
}
