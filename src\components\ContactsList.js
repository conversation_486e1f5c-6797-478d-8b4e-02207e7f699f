'use client';

import { useState, useEffect } from 'react';

export default function ContactsList() {
  const [contacts, setContacts] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');

  // Sample data - replace with actual API call
  useEffect(() => {
    const fetchContacts = async () => {
      setIsLoading(true);
      try {
        // Simulate API call
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        // Sample data
        const sampleContacts = [
          {
            id: 1,
            name: '<PERSON>',
            email: '<EMAIL>',
            phone: '+****************',
            company: 'Acme Corp',
            status: 'Active',
            createdAt: '2024-01-15'
          },
          {
            id: 2,
            name: '<PERSON>',
            email: '<EMAIL>',
            phone: '+****************',
            company: 'Tech Solutions',
            status: 'Active',
            createdAt: '2024-01-14'
          },
          {
            id: 3,
            name: '<PERSON>',
            email: '<EMAIL>',
            phone: '+****************',
            company: 'Global Industries',
            status: 'Inactive',
            createdAt: '2024-01-13'
          }
        ];
        
        setContacts(sampleContacts);
      } catch (error) {
        console.error('Error fetching contacts:', error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchContacts();
  }, []);

  const filteredContacts = contacts.filter(contact =>
    contact.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    contact.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
    contact.company.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <div className="white_card card_height_100">
      <div className="white_card_header">
        <div className="box_header m-0">
          <div className="main-title">
            <h3 className="m-0">Contacts & Leads</h3>
          </div>
          <div className="serach_field-area">
            <div className="search_inner">
              <form>
                <div className="search_field">
                  <input
                    type="text"
                    placeholder="Search contacts..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                  />
                </div>
                <button type="submit" className="serach_button">
                  <i className="ti-search"></i>
                </button>
              </form>
            </div>
          </div>
        </div>
      </div>
      <div className="white_card_body">
        <div className="QA_section">
          <div className="white_box_tittle list_header">
            <h4>Contact List</h4>
            <div className="box_right d-flex lms_block">
              <div className="serach_field_2">
                <div className="search_inner">
                  <form>
                    <div className="search_field">
                      <input
                        type="text"
                        placeholder="Search Here"
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                      />
                    </div>
                    <button type="submit">
                      <i className="ti-search"></i>
                    </button>
                  </form>
                </div>
              </div>
              <div className="add_button ms-2">
                <a href="/create-contact" className="btn_1">Add New</a>
              </div>
            </div>
          </div>
          
          {isLoading ? (
            <div className="text-center p-4">
              <div className="spinner-border" role="status">
                <span className="visually-hidden">Loading...</span>
              </div>
              <p className="mt-2">Loading contacts...</p>
            </div>
          ) : (
            <div className="QA_table mb_30">
              <table className="table lms_table_active">
                <thead>
                  <tr>
                    <th scope="col">Name</th>
                    <th scope="col">Email</th>
                    <th scope="col">Phone</th>
                    <th scope="col">Company</th>
                    <th scope="col">Status</th>
                    <th scope="col">Created</th>
                    <th scope="col">Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {filteredContacts.length > 0 ? (
                    filteredContacts.map((contact) => (
                      <tr key={contact.id}>
                        <td>{contact.name}</td>
                        <td>{contact.email}</td>
                        <td>{contact.phone}</td>
                        <td>{contact.company}</td>
                        <td>
                          <span className={`status_btn ${contact.status === 'Active' ? 'active_btn' : 'inactive_btn'}`}>
                            {contact.status}
                          </span>
                        </td>
                        <td>{contact.createdAt}</td>
                        <td>
                          <div className="action_btns d-flex">
                            <a href={`/contacts/${contact.id}`} className="action_btn mr_10">
                              <i className="ti-eye"></i>
                            </a>
                            <a href={`/contacts/${contact.id}/edit`} className="action_btn mr_10">
                              <i className="ti-pencil"></i>
                            </a>
                            <a href="#" className="action_btn">
                              <i className="ti-trash"></i>
                            </a>
                          </div>
                        </td>
                      </tr>
                    ))
                  ) : (
                    <tr>
                      <td colSpan="7" className="text-center">
                        {searchTerm ? 'No contacts found matching your search.' : 'No contacts available.'}
                      </td>
                    </tr>
                  )}
                </tbody>
              </table>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
