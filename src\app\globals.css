:root {
  --background: #dce9f4;
  --foreground: #333;
  --primary: #ff5c00;
  --secondary: #1a73e8;
  --success: #34c759;
  --warning: #ff9500;
  --danger: #ff3b30;
  --light: #f8f9fa;
  --dark: #343a40;
  --sidebar-bg: #ffffff;
  --content-bg: #dce9f4;
}

body {
  background: var(--content-bg);
  color: var(--foreground);
  font-family: var(--font-mulish), "Mulish", Arial, Helvetica, sans-serif;
  margin: 0;
  padding: 0;
  margin-top: -32px;
}

/* Fix for Next.js client-side only CSS imports */
/* These styles will be applied globally */
img {
  max-width: 100%;
  height: auto;
}

.clear {
  clear: both;
}

/* Basic layout structure */
#wpwrap {
  display: flex;
}

.sidebar {
  width: 270px;
  background: #ffffff; /* White background */
  box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.1);
  height: 100vh;
  position: fixed;
  overflow-y: auto;
  z-index: 1000; /* Ensure sidebar is on top */
  left: 0;
  top: 0;
}

#wpcontent {
  flex: 1;
  margin-left: 270px !important;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  background: var(--content-bg);
  padding: 0 !important;
  width: calc(100% - 270px);
}

#wpbody {
  flex: 1;
  padding: 20px;
}

/* Make sure client-side components render properly */
.client-only {
  display: none;
}

/* Header styles */
.header_iner {
  padding: 8px 15px;
  background: #fff;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.05);
}

.header_right {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header_notification_warp {
  display: flex;
  align-items: center;
  list-style: none;
  margin: 0;
  padding: 0;
}

.header_notification_warp li {
  display: inline-block;
  margin-right: 20px;
}

#adminmenu {
    width: 100%;
    background: transparent;
  }

/* Responsive styles */
@media (max-width: 991px) {
  #adminmenumain {
    left: -270px;
    transition: 0.5s;
  }

  #adminmenumain.show {
    left: 0;
  }

  #wpcontent {
    margin-left: 0 !important;
    padding-left: 15px !important;
    width: 100%;
  }
}

