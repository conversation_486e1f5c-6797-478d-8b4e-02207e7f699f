import AppLayout from '@/components/AppLayout';

export const metadata = {
  title: 'Affiliate Form - Occams Portal',
  description: 'Manage affiliate forms',
};

export default function AffiliateFormPage() {
  return (
    <AppLayout>
      <div className="main_content_iner">
        <div className="container-fluid p-0">
          <div className="dashboard-container">
            <div className="dashboard-header">
              <h1 className="dashboard-title">Affiliate Form</h1>
              <p className="dashboard-subtitle">Manage affiliate forms and submissions</p>
            </div>
            
            {/* Affiliate Form content will go here */}
            <div className="modern-card">
              <div className="modern-card-header">
                <h3 className="modern-card-title">Affiliate Information</h3>
              </div>
              <div className="modern-card-body">
                <p>This page is under construction.</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </AppLayout>
  );
}
