/* ===== DESIGN ENHANCEMENTS FOR OCCAMS PORTAL ===== */

/* Modern Color Palette */
:root {
  --primary-color: #6c27ff;
  --primary-dark: #5a1fd9;
  --primary-light: #8b4eff;
  --secondary-color: #1a73e8;
  --success-color: #34c759;
  --warning-color: #ff9500;
  --danger-color: #ff3b30;
  --info-color: #007aff;
  
  --bg-primary: #f8fafc;
  --bg-secondary: #ffffff;
  --bg-tertiary: #f1f5f9;
  
  --text-primary: #1e293b;
  --text-secondary: #64748b;
  --text-muted: #94a3b8;
  
  --border-color: #e2e8f0;
  --border-light: #f1f5f9;
  
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  
  --radius-sm: 6px;
  --radius-md: 8px;
  --radius-lg: 12px;
  --radius-xl: 16px;
}

/* Enhanced Body Styling */
body {
  background: var(--bg-primary) !important;
  color: var(--text-primary) !important;
  font-family: "Mulish", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif !important;
  line-height: 1.6;
  font-weight: 400;
}

/* ===== SIDEBAR ENHANCEMENTS ===== */
.sidebar {
  background: linear-gradient(180deg, #1e293b 0%, #334155 100%);
  box-shadow: var(--shadow-lg);
  border-right: 1px solid rgba(255, 255, 255, 0.1);
  width: 280px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.sidebar .logo {
  padding: 1.5rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  background: rgba(0, 0, 0, 0.2);
}

.sidebar .logo img {
  max-height: 40px;
  filter: brightness(0) invert(1);
}

/* Enhanced Menu Styling */
.metismenu {
  padding: 1rem 0;
}

.metismenu .nav_title {
  padding: 1rem 1.5rem 0.5rem;
}

.metismenu .nav_title label {
  color: rgba(255, 255, 255, 0.6);
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  margin: 0;
}

.metismenu li a {
  display: flex;
  align-items: center;
  padding: 0.75rem 1.5rem;
  color: rgba(255, 255, 255, 0.8);
  text-decoration: none;
  transition: all 0.2s ease;
  border-radius: 0;
  margin: 0 0.75rem;
  border-radius: var(--radius-md);
  font-weight: 500;
}

.metismenu li a:hover {
  background: rgba(255, 255, 255, 0.1);
  color: #ffffff;
  transform: translateX(4px);
}

.metismenu li a.active,
.metismenu li.mm-active > a {
  background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
  color: #ffffff;
  box-shadow: var(--shadow-md);
  transform: translateX(4px);
}

.metismenu li a i {
  width: 20px;
  height: 20px;
  margin-right: 0.75rem;
  font-size: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Submenu Styling */
.metismenu .mm-collapse {
  background: rgba(0, 0, 0, 0.2);
  margin: 0.25rem 0.75rem;
  border-radius: var(--radius-md);
  overflow: hidden;
}

.metismenu .mm-collapse li a {
  padding: 0.5rem 1rem 0.5rem 3rem;
  margin: 0;
  border-radius: 0;
  font-size: 0.875rem;
  font-weight: 400;
}

.metismenu .mm-collapse li a:hover {
  background: rgba(255, 255, 255, 0.1);
  transform: none;
}

/* ===== HEADER ENHANCEMENTS ===== */
.header_iner {
  background: var(--bg-secondary);
  padding: 1rem 1.5rem;
  box-shadow: var(--shadow-sm);
  border-bottom: 1px solid var(--border-color);
  border-radius: 0 0 var(--radius-lg) var(--radius-lg);
  margin-bottom: 1.5rem;
}

/* Search Field Enhancement */
.serach_field-area .search_inner {
  position: relative;
}

.serach_field-area .search_field input {
  background: var(--bg-tertiary);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-lg);
  padding: 0.75rem 1rem 0.75rem 3rem;
  font-size: 0.875rem;
  transition: all 0.2s ease;
  width: 300px;
}

.serach_field-area .search_field input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(108, 39, 255, 0.1);
  background: var(--bg-secondary);
}

.serach_field-area .serach_button {
  position: absolute;
  left: 1rem;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: var(--text-muted);
  font-size: 1rem;
}

/* Profile Info Enhancement */
.profile_info {
  position: relative;
  display: flex;
  align-items: center;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: var(--radius-lg);
  transition: all 0.2s ease;
}

.profile_info:hover {
  background: var(--bg-tertiary);
}

.profile_info img {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  margin-right: 0.75rem;
  border: 2px solid var(--border-color);
}

.profile_author_name p {
  margin: 0;
  font-weight: 600;
  color: var(--text-primary);
  font-size: 0.875rem;
}

.profile_author_name h5 {
  margin: 0;
  font-size: 0.75rem;
  color: var(--text-secondary);
  font-weight: 500;
}

.profile_info_details {
  position: absolute;
  top: 100%;
  right: 0;
  background: var(--bg-secondary);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-xl);
  min-width: 200px;
  z-index: 1000;
  display: none;
  padding: 0.5rem 0;
  margin-top: 0.5rem;
}

.profile_info_details a {
  display: block;
  padding: 0.75rem 1rem;
  color: var(--text-primary);
  text-decoration: none;
  transition: all 0.2s ease;
  font-size: 0.875rem;
  font-weight: 500;
}

.profile_info_details a:hover {
  background: var(--bg-tertiary);
  color: var(--primary-color);
}

/* Notification Enhancement */
.bell_notification_clicker {
  position: relative;
  padding: 0.75rem;
  border-radius: 50%;
  background: var(--bg-tertiary);
  color: var(--text-secondary);
  transition: all 0.2s ease;
  margin-right: 1rem;
}

.bell_notification_clicker:hover {
  background: var(--primary-color);
  color: white;
  transform: scale(1.05);
}

.notification_count {
  position: absolute;
  top: 0.25rem;
  right: 0.25rem;
  background: var(--danger-color);
  color: white;
  border-radius: 50%;
  width: 18px;
  height: 18px;
  font-size: 0.75rem;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
}

/* ===== MAIN CONTENT ENHANCEMENTS ===== */
.main_content {
  background: var(--bg-primary);
  min-height: 100vh;
}

.main_content_iner {
  padding: 1.5rem;
}

/* ===== CARD ENHANCEMENTS ===== */
.white_card {
  background: var(--bg-secondary);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-md);
  border: 1px solid var(--border-light);
  overflow: hidden;
  transition: all 0.3s ease;
}

.white_card:hover {
  box-shadow: var(--shadow-lg);
  transform: translateY(-2px);
}

.white_card_header {
  background: linear-gradient(135deg, var(--bg-secondary) 0%, var(--bg-tertiary) 100%);
  padding: 1.5rem;
  border-bottom: 1px solid var(--border-color);
}

.white_card_header .main-title h3 {
  margin: 0;
  color: var(--text-primary);
  font-weight: 700;
  font-size: 1.25rem;
}

.white_card_body {
  padding: 1.5rem;
}

/* ===== BUTTON ENHANCEMENTS ===== */
.btn {
  border-radius: var(--radius-md);
  font-weight: 600;
  padding: 0.75rem 1.5rem;
  transition: all 0.2s ease;
  border: none;
  font-size: 0.875rem;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.btn-primary {
  background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
  color: white;
  box-shadow: var(--shadow-sm);
}

.btn-primary:hover {
  background: linear-gradient(135deg, var(--primary-dark), var(--primary-color));
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.btn_1 {
  background: linear-gradient(135deg, var(--primary-color), var(--primary-light)) !important;
  color: white !important;
  border: none !important;
  border-radius: var(--radius-md) !important;
  padding: 0.75rem 1.5rem !important;
  font-weight: 600 !important;
  transition: all 0.2s ease !important;
  text-decoration: none !important;
  display: inline-flex !important;
  align-items: center !important;
  gap: 0.5rem !important;
}

.btn_1:hover {
  background: linear-gradient(135deg, var(--primary-dark), var(--primary-color)) !important;
  transform: translateY(-1px) !important;
  box-shadow: var(--shadow-md) !important;
  color: white !important;
}

/* ===== FORM ENHANCEMENTS ===== */
.form-control {
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  padding: 0.75rem 1rem;
  font-size: 0.875rem;
  transition: all 0.2s ease;
  background: var(--bg-secondary);
}

.form-control:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(108, 39, 255, 0.1);
}

.form-label {
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 0.5rem;
  font-size: 0.875rem;
}

/* ===== FOOTER ENHANCEMENTS ===== */
.footer_part {
  background: var(--bg-secondary);
  border-top: 1px solid var(--border-color);
  padding: 1.5rem;
  margin-top: 2rem;
}

.footer_iner p {
  margin: 0;
  color: var(--text-secondary);
  font-size: 0.875rem;
}

.footer_iner a {
  color: var(--primary-color);
  text-decoration: none;
  font-weight: 600;
}

.footer_iner a:hover {
  color: var(--primary-dark);
}

/* ===== RESPONSIVE ENHANCEMENTS ===== */
@media (max-width: 768px) {
  .sidebar {
    width: 100%;
    position: fixed;
    top: 0;
    left: -100%;
    height: 100vh;
    z-index: 1000;
  }
  
  .sidebar.active_sidebar {
    left: 0;
  }
  
  .main_content_iner {
    padding: 1rem;
  }
  
  .serach_field-area .search_field input {
    width: 200px;
  }
}

/* ===== ANIMATION ENHANCEMENTS ===== */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.white_card {
  animation: fadeInUp 0.5s ease-out;
}

/* ===== LOADING STATES ===== */
.spinner-border-sm {
  width: 1rem;
  height: 1rem;
}

/* ===== STATUS BADGES ===== */
.status_btn {
  padding: 0.25rem 0.75rem;
  border-radius: var(--radius-lg);
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.025em;
}

.active_btn {
  background: rgba(52, 199, 89, 0.1);
  color: var(--success-color);
}

.inactive_btn {
  background: rgba(255, 59, 48, 0.1);
  color: var(--danger-color);
}

/* ===== TABLE ENHANCEMENTS ===== */
.table {
  border-radius: var(--radius-lg);
  overflow: hidden;
  box-shadow: var(--shadow-sm);
}

.table th {
  background: var(--bg-tertiary);
  border: none;
  font-weight: 600;
  color: var(--text-primary);
  padding: 1rem;
  font-size: 0.875rem;
}

.table td {
  border: none;
  padding: 1rem;
  border-bottom: 1px solid var(--border-light);
  color: var(--text-secondary);
}

.table tbody tr:hover {
  background: var(--bg-tertiary);
}

/* ===== ACTION BUTTONS ===== */
.action_btns {
  gap: 0.5rem;
}

.action_btn {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--bg-tertiary);
  color: var(--text-secondary);
  text-decoration: none;
  transition: all 0.2s ease;
  border: 1px solid var(--border-color);
}

.action_btn:hover {
  background: var(--primary-color);
  color: white;
  transform: scale(1.1);
}
