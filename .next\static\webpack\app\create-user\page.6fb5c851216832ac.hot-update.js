"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/create-user/page",{

/***/ "(app-pages-browser)/./src/components/Sidebar.js":
/*!***********************************!*\
  !*** ./src/components/Sidebar.js ***!
  \***********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Sidebar; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction Sidebar() {\n    _s();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.usePathname)();\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isOpen, setIsOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Get user data from localStorage\n        if (true) {\n            const userData = localStorage.getItem(\"user\");\n            if (userData) {\n                setUser(JSON.parse(userData));\n            }\n        }\n        // Handle sidebar toggle\n        const handleSidebarToggle = ()=>{\n            setIsOpen(!isOpen);\n        };\n        // Add event listener for sidebar toggle\n        const sidebarIcon = document.querySelector(\".sidebar_icon\");\n        if (sidebarIcon) {\n            sidebarIcon.addEventListener(\"click\", handleSidebarToggle);\n        }\n        // Cleanup\n        return ()=>{\n            if (sidebarIcon) {\n                sidebarIcon.removeEventListener(\"click\", handleSidebarToggle);\n            }\n        };\n    }, [\n        isOpen\n    ]);\n    const menuItems = [\n        {\n            title: \"Dashboard\",\n            icon: \"ti-dashboard\",\n            path: \"/dashboard\",\n            active: pathname === \"/dashboard\"\n        },\n        {\n            title: \"Contacts\",\n            icon: \"ti-user\",\n            path: \"/contacts\",\n            active: pathname === \"/contacts\"\n        },\n        {\n            title: \"Create User\",\n            icon: \"ti-plus\",\n            path: \"/create-user\",\n            active: pathname === \"/create-user\"\n        },\n        {\n            title: \"Reports\",\n            icon: \"ti-bar-chart\",\n            path: \"/reports\",\n            active: pathname.startsWith(\"/reports\"),\n            submenu: [\n                {\n                    title: \"Lead Reports\",\n                    path: \"/reports/leads\"\n                },\n                {\n                    title: \"Sales Reports\",\n                    path: \"/reports/sales\"\n                },\n                {\n                    title: \"Analytics\",\n                    path: \"/reports/analytics\"\n                }\n            ]\n        },\n        {\n            title: \"Finance\",\n            icon: \"ti-money\",\n            path: \"/finance\",\n            active: pathname.startsWith(\"/finance\"),\n            submenu: [\n                {\n                    title: \"Invoices\",\n                    path: \"/finance/invoices\"\n                },\n                {\n                    title: \"Create Invoice\",\n                    path: \"/finance/create-invoice\"\n                }\n            ]\n        },\n        {\n            title: \"Send Lead\",\n            icon: \"ti-share\",\n            path: \"/send-lead\",\n            active: pathname === \"/send-lead\"\n        },\n        {\n            title: \"Settings\",\n            icon: \"ti-settings\",\n            path: \"/contact-settings\",\n            active: pathname === \"/contact-settings\"\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n        className: \"sidebar \".concat(isOpen ? \"active_sidebar\" : \"\"),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"logo d-flex justify-content-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                        className: \"large_logo\",\n                        href: \"/dashboard\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                            src: \"/assets/images/logo.png\",\n                            alt: \"Occams Portal\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\xampp\\\\htdocs\\\\erc-convert-next\\\\next\\\\src\\\\components\\\\Sidebar.js\",\n                            lineNumber: 98,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\xampp\\\\htdocs\\\\erc-convert-next\\\\next\\\\src\\\\components\\\\Sidebar.js\",\n                        lineNumber: 97,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                        className: \"small_logo\",\n                        href: \"/dashboard\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                            src: \"/assets/images/logo.png\",\n                            alt: \"Occams Portal\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\xampp\\\\htdocs\\\\erc-convert-next\\\\next\\\\src\\\\components\\\\Sidebar.js\",\n                            lineNumber: 101,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\xampp\\\\htdocs\\\\erc-convert-next\\\\next\\\\src\\\\components\\\\Sidebar.js\",\n                        lineNumber: 100,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"sidebar_close_icon d-lg-none\",\n                        onClick: ()=>setIsOpen(false),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                            className: \"ti-close\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\xampp\\\\htdocs\\\\erc-convert-next\\\\next\\\\src\\\\components\\\\Sidebar.js\",\n                            lineNumber: 104,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\xampp\\\\htdocs\\\\erc-convert-next\\\\next\\\\src\\\\components\\\\Sidebar.js\",\n                        lineNumber: 103,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\xampp\\\\htdocs\\\\erc-convert-next\\\\next\\\\src\\\\components\\\\Sidebar.js\",\n                lineNumber: 96,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                id: \"sidebar_menu\",\n                className: \"metismenu\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"nav_title\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                children: \"Main Menu\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\xampp\\\\htdocs\\\\erc-convert-next\\\\next\\\\src\\\\components\\\\Sidebar.js\",\n                                lineNumber: 111,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\xampp\\\\htdocs\\\\erc-convert-next\\\\next\\\\src\\\\components\\\\Sidebar.js\",\n                            lineNumber: 110,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\xampp\\\\htdocs\\\\erc-convert-next\\\\next\\\\src\\\\components\\\\Sidebar.js\",\n                        lineNumber: 109,\n                        columnNumber: 9\n                    }, this),\n                    menuItems.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                            className: item.active ? \"mm-active\" : \"\",\n                            children: item.submenu ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"#\",\n                                        className: item.active ? \"active\" : \"\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                                className: item.icon\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\xampp\\\\htdocs\\\\erc-convert-next\\\\next\\\\src\\\\components\\\\Sidebar.js\",\n                                                lineNumber: 120,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: item.title\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\xampp\\\\htdocs\\\\erc-convert-next\\\\next\\\\src\\\\components\\\\Sidebar.js\",\n                                                lineNumber: 121,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\erc-convert-next\\\\next\\\\src\\\\components\\\\Sidebar.js\",\n                                        lineNumber: 119,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                        className: \"mm-collapse \".concat(item.active ? \"mm-show\" : \"\"),\n                                        children: item.submenu.map((subItem, subIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                    href: subItem.path,\n                                                    className: pathname === subItem.path ? \"active\" : \"\",\n                                                    children: subItem.title\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\xampp\\\\htdocs\\\\erc-convert-next\\\\next\\\\src\\\\components\\\\Sidebar.js\",\n                                                    lineNumber: 126,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, subIndex, false, {\n                                                fileName: \"C:\\\\xampp\\\\htdocs\\\\erc-convert-next\\\\next\\\\src\\\\components\\\\Sidebar.js\",\n                                                lineNumber: 125,\n                                                columnNumber: 21\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\erc-convert-next\\\\next\\\\src\\\\components\\\\Sidebar.js\",\n                                        lineNumber: 123,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                href: item.path,\n                                className: item.active ? \"active\" : \"\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                                        className: item.icon\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\erc-convert-next\\\\next\\\\src\\\\components\\\\Sidebar.js\",\n                                        lineNumber: 135,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: item.title\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\xampp\\\\htdocs\\\\erc-convert-next\\\\next\\\\src\\\\components\\\\Sidebar.js\",\n                                        lineNumber: 136,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\xampp\\\\htdocs\\\\erc-convert-next\\\\next\\\\src\\\\components\\\\Sidebar.js\",\n                                lineNumber: 134,\n                                columnNumber: 15\n                            }, this)\n                        }, index, false, {\n                            fileName: \"C:\\\\xampp\\\\htdocs\\\\erc-convert-next\\\\next\\\\src\\\\components\\\\Sidebar.js\",\n                            lineNumber: 116,\n                            columnNumber: 11\n                        }, this))\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\xampp\\\\htdocs\\\\erc-convert-next\\\\next\\\\src\\\\components\\\\Sidebar.js\",\n                lineNumber: 108,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\xampp\\\\htdocs\\\\erc-convert-next\\\\next\\\\src\\\\components\\\\Sidebar.js\",\n        lineNumber: 95,\n        columnNumber: 5\n    }, this);\n}\n_s(Sidebar, \"F6Da1BVOm/7IJLu23/5IIaurToE=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_3__.usePathname\n    ];\n});\n_c = Sidebar;\nvar _c;\n$RefreshReg$(_c, \"Sidebar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/Sidebar.js\n"));

/***/ })

});