'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';

export default function Header() {
  const [user, setUser] = useState(null);
  const router = useRouter();

  useEffect(() => {
    // Get user data from localStorage
    if (typeof window !== 'undefined') {
      const userData = localStorage.getItem('user');
      if (userData) {
        setUser(JSON.parse(userData));
      }
    }
  }, []);

  const handleLogout = async () => {
    try {
      // Call logout API to clear cookie
      await fetch('/api/auth/logout', {
        method: 'POST',
      });

      // Clear localStorage
      if (typeof window !== 'undefined') {
        localStorage.removeItem('user');
      }

      // Redirect to login
      window.location.href = '/login';
    } catch (error) {
      console.error('Logout error:', error);
      // Force redirect even if API call fails
      window.location.href = '/login';
    }
  };

  return (
    <div className="header_iner d-flex justify-content-between align-items-center">
      <div className="sidebar_icon d-lg-none">
        <i className="ti-menu"></i>
      </div>
      <div className="serach_field-area d-flex align-items-center">
        <div className="search_inner">
          <form>
            <div className="search_field">
              <input type="text" placeholder="Search here..." />
            </div>
            <button type="submit" className="serach_button">
              <i className="ti-search"></i>
            </button>
          </form>
        </div>
      </div>
      <div className="header_right d-flex justify-content-between align-items-center">
        <div className="header_notification_warp d-flex align-items-center">
          <li>
            <a className="bell_notification_clicker" href="#">
              <i className="ti-bell"></i>
              <span className="notification_count">3</span>
            </a>
          </li>
        </div>
        <div className="profile_info">
          <img src="/assets/images/logo.png" alt="Profile" />
          <div className="profile_info_iner">
            <div className="profile_author_name">
              <p>{user?.displayName || 'User'}</p>
              <h5>{user?.role || 'Member'}</h5>
            </div>
            <div className="profile_info_details">
              <a href="/my-profile">My Profile</a>
              <a href="/settings">Settings</a>
              <a href="#" onClick={handleLogout}>Log Out</a>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}