import { render, screen } from '@testing-library/react';
import LeadReportPage from './page';

// Mock the components used in LeadReportPage
jest.mock('@/components/LeadReportTable', () => {
  return function MockLeadReportTable() {
    return <div data-testid="lead-report-table">Lead Report Table Mock</div>;
  };
});

jest.mock('@/components/DashboardLayout', () => {
  return function MockDashboardLayout({ children }) {
    return <div data-testid="dashboard-layout">{children}</div>;
  };
});

describe('LeadReportPage', () => {
  it('renders the LeadReportTable component within DashboardLayout', () => {
    render(<LeadReportPage />);
    
    // Check if DashboardLayout is rendered
    const dashboardLayout = screen.getByTestId('dashboard-layout');
    expect(dashboardLayout).toBeInTheDocument();
    
    // Check if LeadReportTable is rendered within DashboardLayout
    const leadReportTable = screen.getByTestId('lead-report-table');
    expect(leadReportTable).toBeInTheDocument();
    expect(dashboardLayout).toContainElement(leadReportTable);
  });
});
