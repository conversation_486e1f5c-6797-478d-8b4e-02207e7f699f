'use client';

import React from 'react';
import LeadReportTable from '@/components/LeadReportTable';
import DashboardLayout from '@/components/DashboardLayout';

export default function LeadReportPage() {
  return (
    <>
      <DashboardLayout>
        <div className="container-fluid px-0" style={{ width: '100%' }}>
          <div className="action-buttons-container mb-4">
            <div className="d-flex gap-3">
              <button className="btn btn-action">
                <i className="fas fa-paper-plane me-2"></i> Send Lead
              </button>
              <button className="btn btn-action">
                <i className="fas fa-user-plus me-2"></i> Create Sales User
              </button>
              <button className="btn btn-action">
                <i className="fas fa-file-alt me-2"></i> Affiliate Form
              </button>
            </div>
            <div className="welcome-message">
              Welcome, User <i className="fas fa-user-circle ms-1"></i>
            </div>
          </div>
          <div className="row g-0">
            <div className="col-12">
              <LeadReportTable />
            </div>
          </div>
        </div>
      </DashboardLayout>
    </>
  );
}
