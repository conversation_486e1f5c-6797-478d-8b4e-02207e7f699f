import AppLayout from '@/components/AppLayout';

export const metadata = {
  title: 'Create Invoice - Occams Portal',
  description: 'Create a new invoice',
};

export default function CreateInvoicePage() {
  return (
    <AppLayout>
      <div className="main_content_iner">
        <div className="container-fluid p-0">
          <div className="dashboard-container">
            <div className="dashboard-header">
              <h1 className="dashboard-title">Create Invoice</h1>
              <p className="dashboard-subtitle">Create a new invoice</p>
            </div>
            
            {/* Create Invoice content will go here */}
            <div className="modern-card">
              <div className="modern-card-header">
                <h3 className="modern-card-title">Invoice Details</h3>
              </div>
              <div className="modern-card-body">
                <p>This page is under construction.</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </AppLayout>
  );
}
