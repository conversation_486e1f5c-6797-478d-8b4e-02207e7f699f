import { NextResponse } from 'next/server';

// Define which routes require authentication
const protectedRoutes = [
  '/dashboard',
  '/profile',
  // Add other protected routes here
];

export function middleware(request) {
  const { pathname } = request.nextUrl;
  
  // Check if the requested path is a protected route
  const isProtectedRoute = protectedRoutes.some(route => 
    pathname.startsWith(route)
  );
  
  if (isProtectedRoute) {
    // Get the auth token from cookies
    const authToken = request.cookies.get('auth-token')?.value;
    
    // If no token exists, redirect to login
    if (!authToken) {
      const loginUrl = new URL('/login', request.url);
      // Add the original URL as a parameter to redirect after login
      loginUrl.searchParams.set('callbackUrl', pathname);
      return NextResponse.redirect(loginUrl);
    }
    
    // Here you could also verify the token's validity
    // For a complete solution, you'd want to decode and verify the JWT
  }
  
  return NextResponse.next();
}

export const config = {
  matcher: [
    /*
     * Match all request paths except:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public folder
     * - login page
     */
    '/((?!_next/static|_next/image|favicon.ico|public|login).*)',
  ],
};