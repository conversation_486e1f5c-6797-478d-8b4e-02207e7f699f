'use client';

import { useEffect } from 'react';
import Header from './Header';
import Sidebar from './Sidebar';
import Footer from './Footer';

export default function AppLayout({ children }) {
  useEffect(() => {
    // Add necessary classes to body for the admin layout
    document.body.classList.add('dashboard_part', 'large_header_bg');

    // Remove login-page-body class if it exists
    document.body.classList.remove('login-page-body');

    // Cleanup function to remove classes when component unmounts
    return () => {
      document.body.classList.remove('dashboard_part', 'large_header_bg');
    };
  }, []);

  return (
    <div className="app-layout">
      {/* Header */}
      <div className="header_iner d-flex justify-content-between align-items-center">
        <Header />
      </div>

      {/* Main Content Area */}
      <div className="main_content dashboard_part large_header_bg d-flex">
        {/* Sidebar */}
        <Sidebar />

        {/* Main Content */}
        <div className="main_content_iner flex-grow-1">
          <div className="container-fluid p-0">
            {children}
          </div>
        </div>
      </div>

      {/* Footer */}
      <Footer />
    </div>
  );
}
