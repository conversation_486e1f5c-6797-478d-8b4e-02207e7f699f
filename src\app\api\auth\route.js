import { NextResponse } from 'next/server';
import axios from 'axios'; // Import axios

// API route to handle authentication by proxying to WordPress JWT auth
export async function POST(request) {
  try {
    const body = await request.json();
    const { username, password } = body;

    const wordpressAuthUrl = "https://play.occamsadvisory.com/portal/wp-json/jwt-auth/v1/token";

    try {
      const response = await axios.post(wordpressAuthUrl, {
        username,
        password,
      });

      // Assuming the WordPress API returns data in the format:
      // { token, user_email, user_nicename, user_display_name }
      // And we add a success flag.
      const responseData = NextResponse.json({
        success: true,
        ...response.data, // Spread the data from WordPress response
      });

      // Set HTTP-only cookie with the token for middleware
      if (response.data.token) {
        responseData.cookies.set('auth-token', response.data.token, {
          httpOnly: true,
          secure: process.env.NODE_ENV === 'production',
          maxAge: 60 * 60 * 24 * 7, // 1 week
          path: '/',
          sameSite: 'lax'
        });
      }

      return responseData;

    } catch (wpError) {
      // Handle errors from the WordPress API
      // Axios errors have a response object
      if (wpError.response) {
        // Forward the status and message from WordPress if available
        return NextResponse.json(
          { success: false, message: wpError.response.data?.message || 'Authentication failed with WordPress API' },
          { status: wpError.response.status || 401 }
        );
      } else {
        // Network error or other issue calling WordPress
        console.error('WordPress Auth API call error:', wpError.message);
        return NextResponse.json(
          { success: false, message: 'Error connecting to authentication service' },
          { status: 503 } // Service Unavailable
        );
      }
    }
  } catch (error) {
    // Catch errors from request.json() or other unexpected issues
    console.error('Auth API route internal error:', error);
    return NextResponse.json(
      { success: false, message: 'An unexpected server error occurred' },
      { status: 500 }
    );
  }
}

