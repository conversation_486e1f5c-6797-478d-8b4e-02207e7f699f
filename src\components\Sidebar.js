'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';

export default function Sidebar() {
  const pathname = usePathname();
  const [user, setUser] = useState(null);

  useEffect(() => {
    // Get user data from localStorage
    if (typeof window !== 'undefined') {
      const userData = localStorage.getItem('user');
      if (userData) {
        setUser(JSON.parse(userData));
      }
    }
  }, []);

  const menuItems = [
    {
      title: 'Dashboard',
      icon: 'ti-dashboard',
      path: '/dashboard',
      active: pathname === '/dashboard'
    },
    {
      title: 'Contacts',
      icon: 'ti-user',
      path: '/contacts',
      active: pathname === '/contacts'
    },
    {
      title: 'Create User',
      icon: 'ti-plus',
      path: '/create-user',
      active: pathname === '/create-user'
    },
    {
      title: 'Reports',
      icon: 'ti-bar-chart',
      path: '/reports',
      active: pathname.startsWith('/reports'),
      submenu: [
        { title: 'Lead Reports', path: '/reports/leads' },
        { title: 'Sales Reports', path: '/reports/sales' },
        { title: 'Analytics', path: '/reports/analytics' }
      ]
    },
    {
      title: 'Finance',
      icon: 'ti-money',
      path: '/finance',
      active: pathname.startsWith('/finance'),
      submenu: [
        { title: 'Invoices', path: '/finance/invoices' },
        { title: 'Create Invoice', path: '/finance/create-invoice' }
      ]
    },
    {
      title: 'Send Lead',
      icon: 'ti-share',
      path: '/send-lead',
      active: pathname === '/send-lead'
    },
    {
      title: 'Settings',
      icon: 'ti-settings',
      path: '/contact-settings',
      active: pathname === '/contact-settings'
    }
  ];

  return (
    <nav className="sidebar">
      <div className="logo d-flex justify-content-between">
        <Link className="large_logo" href="/dashboard">
          <img src="/assets/images/logo.png" alt="Occams Portal" />
        </Link>
        <Link className="small_logo" href="/dashboard">
          <img src="/assets/images/logo.png" alt="Occams Portal" />
        </Link>
        <div className="sidebar_close_icon d-lg-none">
          <i className="ti-close"></i>
        </div>
      </div>

      <ul id="sidebar_menu" className="metismenu">
        <li>
          <div className="nav_title">
            <label>Main Menu</label>
          </div>
        </li>

        {menuItems.map((item, index) => (
          <li key={index} className={item.active ? 'mm-active' : ''}>
            {item.submenu ? (
              <>
                <a href="#" className={item.active ? 'active' : ''}>
                  <i className={item.icon}></i>
                  <span>{item.title}</span>
                </a>
                <ul className={`mm-collapse ${item.active ? 'mm-show' : ''}`}>
                  {item.submenu.map((subItem, subIndex) => (
                    <li key={subIndex}>
                      <Link href={subItem.path} className={pathname === subItem.path ? 'active' : ''}>
                        {subItem.title}
                      </Link>
                    </li>
                  ))}
                </ul>
              </>
            ) : (
              <Link href={item.path} className={item.active ? 'active' : ''}>
                <i className={item.icon}></i>
                <span>{item.title}</span>
              </Link>
            )}
          </li>
        ))}
      </ul>
    </nav>
  );
}