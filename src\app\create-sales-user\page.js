import AppLayout from '@/components/AppLayout';

export const metadata = {
  title: 'Create Sales User - Occams Portal',
  description: 'Create a new sales user',
};

export default function CreateSalesUserPage() {
  return (
    <AppLayout>
      <div className="main_content_iner">
        <div className="container-fluid p-0">
          <div className="dashboard-container">
            <div className="dashboard-header">
              <h1 className="dashboard-title">Create Sales User</h1>
              <p className="dashboard-subtitle">Add a new sales representative to the system</p>
            </div>
            
            {/* Create Sales User content will go here */}
            <div className="modern-card">
              <div className="modern-card-header">
                <h3 className="modern-card-title">Sales User Information</h3>
              </div>
              <div className="modern-card-body">
                <p>This page is under construction.</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </AppLayout>
  );
}
