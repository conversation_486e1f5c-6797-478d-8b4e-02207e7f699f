/* Lead Report Styles */
.lead-report-container {
  background-color: white;
  border-radius: 0;
  box-shadow: none;
  margin-bottom: 30px;
  width: 100%;
  overflow: hidden;
}

.lead-report-header {
  background: linear-gradient(90deg, #1e5799 0%, #207cca 100%);
  color: white;
  padding: 15px 20px;
  font-size: 18px;
  font-weight: 500;
  display: flex;
  align-items: center;
  border-radius: 0;
}

.lead-report-header i {
  margin-right: 10px;
  font-size: 20px;
  color: white;
}

.lead-report-content {
  padding: 15px;
  background-color: white;
  overflow: hidden;
}

.lead-report-filters {
  margin-bottom: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.search-container {
  position: relative;
  max-width: 300px;
}

.search-container input {
  border-radius: 4px 0 0 4px;
  border: 1px solid #ddd;
  padding: 8px 12px;
  height: 38px;
}

.search-btn {
  background-color: #ff5722;
  border: 1px solid #ff5722;
  border-left: none;
  border-radius: 0 4px 4px 0;
  color: white;
  padding: 8px 12px;
}

.export-buttons {
  display: flex;
  gap: 5px;
}

.export-btn {
  border-radius: 4px;
  padding: 6px 12px;
  font-size: 14px;
  font-weight: 500;
}

.excel-btn, .pdf-btn, .csv-btn {
  background-color: #ff5722;
  border-color: #ff5722;
  color: white;
  border-radius: 20px;
  padding: 5px 15px;
}

/* Table Styles */
.table-responsive {
  width: 100%;
  overflow-x: auto;
  display: block;
  -webkit-overflow-scrolling: touch;
  max-width: 100%;
  margin-bottom: 0;
  border: none;
  max-height: none;
}

.lead-report-table {
  width: 100%;
  min-width: 1200px; /* Force horizontal scrolling on smaller screens */
  border-collapse: collapse;
  table-layout: fixed;
  margin-bottom: 0;
  border: 1px solid #e9ecef;
}

.lead-report-table thead {
  background-color: white;
}

.lead-report-table th {
  padding: 10px 15px;
  text-align: left;
  font-weight: 500;
  color: #1e5799;
  border-bottom: 2px solid #1e5799;
  white-space: nowrap;
  background-color: white;
  font-size: 14px;
}

.lead-report-table td {
  padding: 10px 15px;
  border-bottom: 1px solid #e9ecef;
  vertical-align: middle;
  font-size: 14px;
}

.lead-report-table tbody tr:nth-child(odd) {
  background-color: #f8f9fa;
}

.lead-report-table tbody tr:hover {
  background-color: #f0f4f8;
}

.lead-report-table tbody tr:nth-child(even) {
  background-color: white;
}

/* Status Badges */
.status-badge {
  display: inline-block;
  padding: 5px 10px;
  border-radius: 30px;
  font-size: 12px;
  font-weight: 500;
  text-align: center;
  width: 100%;
  max-width: 100px;
  margin: 0 auto;
}

.status-new {
  background-color: #00b8d4;
  color: white;
}

.status-converted {
  background-color: #6c757d;
  color: white;
}

.status-qualified {
  background-color: #ffc107;
  color: #212529;
}

.status-active {
  background-color: #28a745;
  color: white;
}

/* Action Buttons */
.action-buttons {
  display: flex;
  justify-content: center;
  gap: 5px;
}

.action-btn {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
  border-radius: 4px;
}

.contact-card-btn, .book-call-btn {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
  border-radius: 4px;
  background-color: #1e5799;
  border-color: #1e5799;
  color: white;
}

/* Pagination */
.lead-report-pagination {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 20px;
}

.pagination-info p {
  margin: 0;
  color: #6c757d;
  font-size: 14px;
}

.pagination-controls {
  display: flex;
  align-items: center;
  gap: 5px;
}

.pagination-btn {
  background-color: white;
  border: 1px solid #dee2e6;
  color: #495057;
  padding: 5px 10px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
}

.pagination-btn.active {
  background-color: #1e5799;
  border-color: #1e5799;
  color: white;
}

.pagination-btn:hover:not(.active) {
  background-color: #f8f9fa;
}

.pagination-ellipsis {
  color: #6c757d;
  padding: 0 5px;
}

.prev-btn, .next-btn {
  padding: 5px 10px;
}

/* Agent Name Styling */
.agent-name {
  color: #1e5799;
  font-weight: 500;
}
